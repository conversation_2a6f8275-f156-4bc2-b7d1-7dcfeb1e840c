# 待办任务查询接口文档

## 接口概述

待办任务查询相关接口用于查询当前登录用户的待办任务，支持分页查询、条件筛选、任务详情查看等功能。

## 接口列表

### 1. 分页查询待办任务

- **接口地址**: `GET /workflow/task/todo/page`
- **接口说明**: 分页查询当前用户的待办任务列表
- **请求方式**: GET
- **权限标识**: `workflow:task:list`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页条数，默认10 |
| processName | String | 否 | 流程名称（模糊查询） |
| categoryId | String | 否 | 流程分类ID |
| processTitle | String | 否 | 流程标题（模糊查询） |
| taskName | String | 否 | 任务名称（模糊查询） |
| startUserId | String | 否 | 流程发起人ID |
| taskCreateTimeStart | Date | 否 | 任务创建时间-开始（格式：yyyy-MM-dd HH:mm:ss） |
| taskCreateTimeEnd | Date | 否 | 任务创建时间-结束（格式：yyyy-MM-dd HH:mm:ss） |
| startTimeBegin | Date | 否 | 流程发起时间-开始（格式：yyyy-MM-dd HH:mm:ss） |
| startTimeEnd | Date | 否 | 流程发起时间-结束（格式：yyyy-MM-dd HH:mm:ss） |
| urgency | Integer | 否 | 紧急程度：0-普通，1-紧急，2-特急 |
| onlyMyStart | Boolean | 否 | 是否只查询我发起的 |
| onlyAssignedToMe | Boolean | 否 | 是否只查询指派给我的 |
| includeDelegated | Boolean | 否 | 是否包含委托任务 |
| orderByColumn | String | 否 | 排序字段：taskCreateTime、dueDate、priority |
| isAsc | String | 否 | 排序方式：asc、desc |

#### 响应示例

```json
{
  "total": 50,
  "rows": [
    {
      "taskId": "2501",
      "taskName": "部门经理审批",
      "taskDefinitionKey": "Activity_0x1y2z3",
      "processInstanceId": "2401",
      "processDefinitionId": "leave_process:1:2301",
      "processDefinitionKey": "leave_process",
      "processName": "请假流程",
      "processTitle": "张三的请假申请",
      "startUserId": "1001",
      "startUserName": "张三",
      "startTime": "2025-01-20 09:00:00",
      "taskCreateTime": "2025-01-20 09:30:00",
      "dueDate": "2025-01-21 09:30:00",
      "priority": 50,
      "taskStatus": "ACTIVE",
      "suspended": false,
      "assignee": "1002",
      "categoryId": "1001",
      "categoryName": "人事管理",
      "formId": 2001,
      "formName": "请假申请表",
      "urgency": 0,
      "version": 1,
      "canWithdraw": false,
      "canTransfer": true,
      "canDelegate": true,
      "canAddSign": true,
      "processVariables": {
        "leaveType": "1",
        "leaveDays": 2,
        "processTitle": "张三的请假申请"
      }
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

### 2. 查询待办任务列表

- **接口地址**: `GET /workflow/task/todo/list`
- **接口说明**: 查询当前用户的所有待办任务（不分页）
- **请求方式**: GET
- **权限标识**: `workflow:task:list`

#### 请求参数

与分页查询相同，但不包含分页参数（pageNum、pageSize）

#### 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "2501",
      "taskName": "部门经理审批",
      // ... 其他字段同分页查询
    }
  ]
}
```

### 3. 查询待办任务数量

- **接口地址**: `GET /workflow/task/todo/count`
- **接口说明**: 查询当前用户的待办任务总数
- **请求方式**: GET
- **权限标识**: `workflow:task:list`

#### 请求参数

无

#### 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": 5
}
```

### 4. 获取待办任务详情

- **接口地址**: `GET /workflow/task/todo/{taskId}`
- **接口说明**: 根据任务ID获取待办任务的详细信息
- **请求方式**: GET
- **权限标识**: `workflow:task:query`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| taskId | String | 是 | 任务ID（路径参数） |

#### 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "2501",
    "taskName": "部门经理审批",
    "taskDefinitionKey": "Activity_0x1y2z3",
    "processInstanceId": "2401",
    "processDefinitionId": "leave_process:1:2301",
    "processDefinitionKey": "leave_process",
    "processName": "请假流程",
    "processTitle": "张三的请假申请",
    "startUserId": "1001",
    "startUserName": "张三",
    "startTime": "2025-01-20 09:00:00",
    "taskCreateTime": "2025-01-20 09:30:00",
    "dueDate": "2025-01-21 09:30:00",
    "priority": 50,
    "taskStatus": "ACTIVE",
    "suspended": false,
    "owner": null,
    "assignee": "1002",
    "delegationState": null,
    "businessKey": "LEAVE_20250120_001",
    "categoryId": "1001",
    "categoryName": "人事管理",
    "formId": 2001,
    "formName": "请假申请表",
    "processVariables": {
      "leaveType": "1",
      "leaveDays": 2,
      "startDate": "2025-01-20",
      "endDate": "2025-01-21",
      "reason": "家中有事",
      "processTitle": "张三的请假申请",
      "urgency": 0
    },
    "taskLocalVariables": {},
    "canWithdraw": false,
    "canTransfer": true,
    "canDelegate": true,
    "canAddSign": true,
    "urgency": 0,
    "version": 1,
    "tenantId": "1"
  }
}
```

### 5. 查询我的待办任务

- **接口地址**: `GET /workflow/task/todo/my`
- **接口说明**: 查询当前用户的所有待办任务，包含代理和委托任务
- **请求方式**: GET
- **权限标识**: `workflow:task:list`

#### 请求参数

无

#### 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "2501",
      "taskName": "部门经理审批",
      "assignee": "1002",
      "delegationState": null,
      // ... 其他字段
    },
    {
      "taskId": "2502",
      "taskName": "财务审核",
      "assignee": null,  // 候选任务
      "delegationState": null,
      // ... 其他字段
    }
  ]
}
```

## 字段说明

### TodoTaskVo 待办任务对象

| 字段名 | 类型 | 说明 |
|--------|------|------|
| taskId | String | 任务ID |
| taskName | String | 任务名称 |
| taskDefinitionKey | String | 任务定义Key |
| processInstanceId | String | 流程实例ID |
| processDefinitionId | String | 流程定义ID |
| processDefinitionKey | String | 流程定义Key |
| processName | String | 流程名称 |
| processTitle | String | 流程标题 |
| startUserId | String | 流程发起人ID |
| startUserName | String | 流程发起人姓名 |
| startTime | Date | 流程发起时间 |
| taskCreateTime | Date | 任务创建时间 |
| dueDate | Date | 任务到期时间 |
| priority | Integer | 任务优先级（0-100，数值越大优先级越高） |
| taskStatus | String | 任务状态 |
| suspended | Boolean | 是否挂起 |
| owner | String | 任务所有者 |
| assignee | String | 任务办理人 |
| delegationState | String | 委托状态：PENDING-待处理，RESOLVED-已解决 |
| businessKey | String | 业务Key |
| categoryId | String | 流程分类ID |
| categoryName | String | 流程分类名称 |
| formId | Long | 表单ID |
| formName | String | 表单名称 |
| processVariables | Map | 流程变量 |
| taskLocalVariables | Map | 任务本地变量 |
| canWithdraw | Boolean | 是否可以撤回 |
| canTransfer | Boolean | 是否可以转办 |
| canDelegate | Boolean | 是否可以委托 |
| canAddSign | Boolean | 是否可以加签 |
| urgency | Integer | 流程紧急程度：0-普通，1-紧急，2-特急 |
| version | Integer | 流程版本 |
| tenantId | String | 租户ID |

## 使用示例

### 1. 查询所有待办任务
```bash
curl -X GET "http://192.168.1.198:8081/workflow/task/todo/page?pageNum=1&pageSize=10" \
  -H "Authorization: Bearer {token}"
```

### 2. 按条件筛选待办任务
```bash
curl -X GET "http://192.168.1.198:8081/workflow/task/todo/page?processName=请假&urgency=1&orderByColumn=taskCreateTime&isAsc=desc" \
  -H "Authorization: Bearer {token}"
```

### 3. 获取待办任务详情
```bash
curl -X GET "http://192.168.1.198:8081/workflow/task/todo/2501" \
  -H "Authorization: Bearer {token}"
```

### 4. 获取待办任务数量（用于消息提醒）
```bash
curl -X GET "http://192.168.1.198:8081/workflow/task/todo/count" \
  -H "Authorization: Bearer {token}"
```

## 注意事项

1. 所有接口都需要用户登录认证，请在请求头中携带有效的 token
2. 查询结果只返回当前登录用户的待办任务
3. 待办任务包括直接分配给用户的任务和用户作为候选人的任务
4. 任务的 `assignee` 字段为空时，表示该任务是候选任务，需要先认领才能处理
5. `delegationState` 字段表示任务的委托状态，如果不为空说明该任务是委托任务
6. 流程变量和任务变量中可能包含敏感信息，请注意数据安全

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 401 | 未授权，请登录 |
| 403 | 无权限访问 |
| 404 | 任务不存在 |
| 500 | 服务器内部错误 |