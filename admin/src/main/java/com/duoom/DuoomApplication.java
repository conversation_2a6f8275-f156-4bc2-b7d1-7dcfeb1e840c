package com.duoom;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 启动程序
 *
 * <AUTHOR>
 * @version 25.5.0
 */

@SpringBootApplication
public class DuoomApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(DuoomApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println(
                " ▄▄▄▄    ▄                    ▄               █ \n" +
                "█▀   ▀ ▄▄█▄▄   ▄▄▄    ▄ ▄▄  ▄▄█▄▄   ▄▄▄    ▄▄▄█ \n" +
                "▀█▄▄▄    █    ▀   █   █▀  ▀   █    █▀  █  █▀ ▀█ \n" +
                "    ▀█   █    ▄▀▀▀█   █       █    █▀▀▀▀  █   █ \n" +
                "▀▄▄▄█▀   ▀▄▄  ▀▄▄▀█   █       ▀▄▄  ▀█▄▄▀  ▀█▄██ \n"
        );
    }

}
