package com.duoom.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

@Component
public class CamundaTable<PERSON>he<PERSON> implements CommandLineRunner {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(String... args) throws Exception {
        // Check if this is a test run for Camunda tables
        if (args.length > 0 && "check-camunda".equals(args[0])) {
            checkCamundaTables();
            System.exit(0);
        }
    }
    
    public void checkCamundaTables() {
        System.out.println("\n=== Checking Camunda Database Tables ===\n");
        
        // 1. Check ACT_RE_DEPLOYMENT table
        checkTable("ACT_RE_DEPLOYMENT", "Deployed processes");
        
        // 2. Check ACT_RE_PROCDEF table
        checkTable("ACT_RE_PROCDEF", "Process definitions");
        
        // 3. Check ACT_RU_EXECUTION table
        checkTable("ACT_RU_EXECUTION", "Running process instances");
        
        // 4. Check ACT_HI_PROCINST table
        checkTable("ACT_HI_PROCINST", "Historical process instances");
        
        // 5. Check ACT_ID_USER table
        checkTable("ACT_ID_USER", "Camunda users");
        
        // Additional check for running tasks
        checkTable("ACT_RU_TASK", "Running tasks");
        
        // Check for process variables
        checkTable("ACT_RU_VARIABLE", "Running process variables");
    }
    
    private void checkTable(String tableName, String description) {
        try {
            // Check if table exists
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
            Integer tableExists = jdbcTemplate.queryForObject(checkTableSql, Integer.class, tableName);
            
            if (tableExists == 0) {
                System.out.println("❌ Table " + tableName + " does not exist!");
                return;
            }
            
            // Get row count
            String countSql = "SELECT COUNT(*) FROM " + tableName;
            Integer count = jdbcTemplate.queryForObject(countSql, Integer.class);
            
            System.out.println("\n📊 " + tableName + " (" + description + "):");
            System.out.println("   Total records: " + count);
            
            // Show sample data if table has records
            if (count > 0) {
                String sampleSql = "SELECT * FROM " + tableName + " LIMIT 5";
                List<Map<String, Object>> results = jdbcTemplate.queryForList(sampleSql);
                
                if (!results.isEmpty()) {
                    System.out.println("   Sample data:");
                    for (Map<String, Object> row : results) {
                        System.out.println("   - " + row);
                    }
                }
                
                // Special queries for specific tables
                if ("ACT_RE_PROCDEF".equals(tableName)) {
                    String processDefSql = "SELECT ID_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_ FROM ACT_RE_PROCDEF";
                    List<Map<String, Object>> processDefs = jdbcTemplate.queryForList(processDefSql);
                    System.out.println("\n   Process Definitions:");
                    for (Map<String, Object> pd : processDefs) {
                        System.out.println("   - Key: " + pd.get("KEY_") + 
                                         ", Name: " + pd.get("NAME_") + 
                                         ", Version: " + pd.get("VERSION_") +
                                         ", ID: " + pd.get("ID_"));
                    }
                } else if ("ACT_RU_EXECUTION".equals(tableName)) {
                    String executionSql = "SELECT ID_, PROC_INST_ID_, BUSINESS_KEY_, PROC_DEF_ID_, IS_ACTIVE_ FROM ACT_RU_EXECUTION WHERE PARENT_ID_ IS NULL";
                    List<Map<String, Object>> executions = jdbcTemplate.queryForList(executionSql);
                    System.out.println("\n   Running Process Instances:");
                    for (Map<String, Object> exec : executions) {
                        System.out.println("   - Instance ID: " + exec.get("PROC_INST_ID_") + 
                                         ", Business Key: " + exec.get("BUSINESS_KEY_") +
                                         ", Active: " + exec.get("IS_ACTIVE_"));
                    }
                } else if ("ACT_HI_PROCINST".equals(tableName)) {
                    String histSql = "SELECT ID_, PROC_DEF_ID_, BUSINESS_KEY_, START_TIME_, END_TIME_, STATE_ FROM ACT_HI_PROCINST ORDER BY START_TIME_ DESC LIMIT 5";
                    List<Map<String, Object>> history = jdbcTemplate.queryForList(histSql);
                    System.out.println("\n   Recent Process Instances:");
                    for (Map<String, Object> hist : history) {
                        System.out.println("   - Instance ID: " + hist.get("ID_") + 
                                         ", Business Key: " + hist.get("BUSINESS_KEY_") +
                                         ", State: " + hist.get("STATE_") +
                                         ", Started: " + hist.get("START_TIME_"));
                    }
                } else if ("ACT_ID_USER".equals(tableName)) {
                    String userSql = "SELECT ID_, FIRST_, LAST_, EMAIL_ FROM ACT_ID_USER";
                    List<Map<String, Object>> users = jdbcTemplate.queryForList(userSql);
                    System.out.println("\n   Camunda Users:");
                    for (Map<String, Object> user : users) {
                        System.out.println("   - ID: " + user.get("ID_") + 
                                         ", Name: " + user.get("FIRST_") + " " + user.get("LAST_") +
                                         ", Email: " + user.get("EMAIL_"));
                    }
                }
            } else {
                System.out.println("   ⚠️  Table is empty - no data found");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error checking table " + tableName + ": " + e.getMessage());
        }
    }
}