package com.duoom.test;

import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

@SpringBootTest
@ActiveProfiles("dev")
public class DiagnoseCamundaCockpitIssue {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RepositoryService repositoryService;
    
    @Test
    public void diagnoseWhyProcessNotInCockpit() {
        System.out.println("\n=== 诊断为什么流程在Cockpit中看不到 ===\n");
        
        String processKey = "leave_approval";
        String deploymentId = "86658ea6-4db4-11f0-bb09-9ad7593817c1";
        
        // 1. 通过Java API查询流程定义
        System.out.println("1. 通过Camunda Java API查询:");
        System.out.println("   a) 查询所有流程定义:");
        List<ProcessDefinition> allDefs = repositoryService.createProcessDefinitionQuery().list();
        System.out.println("      总数: " + allDefs.size());
        for (ProcessDefinition pd : allDefs) {
            System.out.println("      - Key: " + pd.getKey() + ", Name: " + pd.getName() + 
                             ", Version: " + pd.getVersion() + ", Tenant: " + pd.getTenantId());
        }
        
        System.out.println("\n   b) 按Key查询:");
        List<ProcessDefinition> byKey = repositoryService.createProcessDefinitionQuery()
            .processDefinitionKey(processKey)
            .list();
        System.out.println("      找到 " + byKey.size() + " 个流程定义");
        
        System.out.println("\n   c) 包含无租户的查询:");
        List<ProcessDefinition> withoutTenant = repositoryService.createProcessDefinitionQuery()
            .processDefinitionKey(processKey)
            .withoutTenantId()
            .list();
        System.out.println("      无租户ID的流程定义: " + withoutTenant.size() + " 个");
        
        // 2. 直接查询数据库
        System.out.println("\n2. 直接查询数据库:");
        
        // 查询部署表
        System.out.println("   a) ACT_RE_DEPLOYMENT 表:");
        String deploymentQuery = "SELECT ID_, NAME_, DEPLOY_TIME_, SOURCE_, TENANT_ID_ FROM ACT_RE_DEPLOYMENT WHERE ID_ = ?";
        List<Map<String, Object>> deployments = jdbcTemplate.queryForList(deploymentQuery, deploymentId);
        for (Map<String, Object> d : deployments) {
            System.out.println("      " + d);
        }
        
        // 查询流程定义表
        System.out.println("\n   b) ACT_RE_PROCDEF 表:");
        String procDefQuery = "SELECT ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, " +
                             "RESOURCE_NAME_, SUSPENSION_STATE_, TENANT_ID_ FROM ACT_RE_PROCDEF WHERE KEY_ = ?";
        List<Map<String, Object>> procDefs = jdbcTemplate.queryForList(procDefQuery, processKey);
        for (Map<String, Object> pd : procDefs) {
            System.out.println("      " + pd);
            
            // 检查关键字段
            if (pd.get("SUSPENSION_STATE_") != null && (Integer)pd.get("SUSPENSION_STATE_") == 2) {
                System.out.println("      ⚠️  警告: 流程被挂起了!");
            }
            if (pd.get("TENANT_ID_") != null && !pd.get("TENANT_ID_").toString().isEmpty()) {
                System.out.println("      ⚠️  警告: 流程有租户ID: " + pd.get("TENANT_ID_"));
            }
        }
        
        // 3. 检查流程资源
        System.out.println("\n3. 检查流程资源:");
        String resourceQuery = "SELECT ID_, REV_, NAME_, DEPLOYMENT_ID_, BYTES_ FROM ACT_GE_BYTEARRAY WHERE DEPLOYMENT_ID_ = ?";
        List<Map<String, Object>> resources = jdbcTemplate.queryForList(resourceQuery, deploymentId);
        for (Map<String, Object> r : resources) {
            System.out.println("   资源: " + r.get("NAME_") + " (ID: " + r.get("ID_") + ")");
            // 检查BYTES_是否为空
            if (r.get("BYTES_") == null) {
                System.out.println("   ⚠️  警告: 资源内容为空!");
            }
        }
        
        // 4. 检查Camunda权限表
        System.out.println("\n4. 检查Camunda权限表:");
        
        // 检查是否存在授权表
        String authTableCheck = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'ACT_RU_AUTHORIZATION'";
        Integer authTableExists = jdbcTemplate.queryForObject(authTableCheck, Integer.class);
        
        if (authTableExists > 0) {
            String authQuery = "SELECT * FROM ACT_RU_AUTHORIZATION WHERE RESOURCE_TYPE_ = 6"; // 6 = Process Definition
            List<Map<String, Object>> auths = jdbcTemplate.queryForList(authQuery);
            System.out.println("   流程定义权限记录: " + auths.size() + " 条");
            for (Map<String, Object> auth : auths) {
                System.out.println("   - " + auth);
            }
        } else {
            System.out.println("   ACT_RU_AUTHORIZATION 表不存在");
        }
        
        // 5. 检查Cockpit可能的过滤条件
        System.out.println("\n5. 可能的原因分析:");
        
        // 检查是否有多租户配置
        String tenantQuery = "SELECT DISTINCT TENANT_ID_ FROM ACT_RE_PROCDEF WHERE TENANT_ID_ IS NOT NULL AND TENANT_ID_ != ''";
        List<Map<String, Object>> tenants = jdbcTemplate.queryForList(tenantQuery);
        if (!tenants.isEmpty()) {
            System.out.println("   ⚠️  系统中存在租户ID: " + tenants);
            System.out.println("   Cockpit可能根据当前用户的租户ID过滤流程定义");
        }
        
        // 检查流程定义的状态
        String suspendedQuery = "SELECT COUNT(*) FROM ACT_RE_PROCDEF WHERE SUSPENSION_STATE_ = 2";
        Integer suspendedCount = jdbcTemplate.queryForObject(suspendedQuery, Integer.class);
        if (suspendedCount > 0) {
            System.out.println("   ⚠️  有 " + suspendedCount + " 个流程定义被挂起");
        }
        
        // 6. 检查当前用户的租户信息
        System.out.println("\n6. 检查Camunda用户和租户关系:");
        String userQuery = "SELECT * FROM ACT_ID_USER WHERE ID_ = 'admin'";
        List<Map<String, Object>> users = jdbcTemplate.queryForList(userQuery);
        for (Map<String, Object> user : users) {
            System.out.println("   Admin用户: " + user);
        }
        
        // 检查租户成员表
        String tenantMemberCheck = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'ACT_ID_TENANT_MEMBER'";
        Integer tenantMemberExists = jdbcTemplate.queryForObject(tenantMemberCheck, Integer.class);
        
        if (tenantMemberExists > 0) {
            String tenantMemberQuery = "SELECT * FROM ACT_ID_TENANT_MEMBER WHERE USER_ID_ = 'admin'";
            List<Map<String, Object>> tenantMembers = jdbcTemplate.queryForList(tenantMemberQuery);
            System.out.println("   Admin用户的租户关系: " + tenantMembers);
        }
        
        System.out.println("\n=== 诊断完成 ===");
    }
}