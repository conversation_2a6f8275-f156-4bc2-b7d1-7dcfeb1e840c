package com.duoom.test;

import com.duoom.workflow.service.IFlwDeployService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.junit.jupiter.api.Test;
import org.camunda.bpm.engine.RepositoryService;

import java.util.List;
import java.util.Map;

@SpringBootTest
@ActiveProfiles("local")
public class FixProcessIdAndRedeploy {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private IFlwDeployService deployService;
    
    @Autowired
    private RepositoryService repositoryService;

    @Test
    public void redeployWithNewProcessId() {
        System.out.println("=== 重新部署流程以使用新的 process id ===\n");
        
        // 查询需要重新部署的流程
        String query = "SELECT id, code, name FROM flow_schema WHERE code = 'leave_approval_test' AND activity_flag = 'Y'";
        List<Map<String, Object>> schemas = jdbcTemplate.queryForList(query);
        
        if (schemas.isEmpty()) {
            System.out.println("未找到活跃的 leave_approval_test 流程");
            return;
        }
        
        for (Map<String, Object> schema : schemas) {
            Long schemaId = Long.valueOf(schema.get("id").toString());
            String code = (String) schema.get("code");
            String name = (String) schema.get("name");
            
            System.out.println("重新部署流程: " + name + " (code: " + code + ", id: " + schemaId + ")");
            
            try {
                // 调用部署服务重新部署
                boolean success = deployService.deploy(schemaId);
                
                if (success) {
                    System.out.println("✓ 流程重新部署成功");
                    
                    // 查询新的流程定义
                    String checkSql = "SELECT definition_key, definition_id FROM flow_schema WHERE id = ?";
                    Map<String, Object> result = jdbcTemplate.queryForMap(checkSql, schemaId);
                    System.out.println("  新的 definition_key: " + result.get("definition_key"));
                    System.out.println("  新的 definition_id: " + result.get("definition_id"));
                } else {
                    System.out.println("✗ 流程重新部署失败");
                }
                
            } catch (Exception e) {
                System.err.println("✗ 重新部署失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        System.out.println("\n现在检查 Camunda Cockpit，应该能看到新的流程定义了");
    }
    
    @Test
    public void listAllProcessDefinitions() {
        System.out.println("\n=== 列出所有流程定义 ===\n");
        
        repositoryService.createProcessDefinitionQuery()
            .orderByProcessDefinitionKey()
            .orderByProcessDefinitionVersion()
            .desc()
            .list()
            .forEach(pd -> {
                System.out.println("Process Definition:");
                System.out.println("  Key: " + pd.getKey());
                System.out.println("  Name: " + pd.getName());
                System.out.println("  Version: " + pd.getVersion());
                System.out.println("  ID: " + pd.getId());
                System.out.println("  Tenant: " + pd.getTenantId());
                System.out.println();
            });
    }
}