<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.duoom</groupId>
        <artifactId>modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-sse</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-mail</artifactId>
        </dependency>

        <!--   解析汉字拼音-地区模块使用     -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <!--  screw 数据库文档生成（代码方式）  -->
        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId>
        </dependency>

        <!--   获取服务器信息依赖   -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
        </dependency>

    </dependencies>

</project>
