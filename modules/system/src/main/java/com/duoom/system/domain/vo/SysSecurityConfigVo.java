package com.duoom.system.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.duoom.system.domain.SysSecurityConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 系统安全设置视图对象 sys_security_config
 *
 * <AUTHOR>
 * @date 2023-02-07
 */
@Data
@AutoMapper(target = SysSecurityConfig.class)
public class SysSecurityConfigVo implements Serializable {

    /**
     * 配置主键
     */
    @TableId(value = "config_id")
    private Long configId;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 登录方式 0 单一登录 1 同时登录
     */
    private Integer loginMethod;

    /**
     * 超时登出
     */
    private Integer logoutTimeout;

    /**
     * 密码错误次数
     */
    private Integer passwordErrorNumber;

    /**
     * 协议状态【Y:正常；N:关闭】
     */
    private String accountLockStatus;

    /**
     * 协议状态【Y:正常；N:关闭】
     */
    private String delayLoginStatus;

    /**
     * 延时时间
     */
    private Integer delayLoginTime;

    /**
     * 登录验证码状态 【Y:正常；N:关闭】
     */
    private String loginCodeStatus;

    /**
     * 验证码类型 0 数组计算 1 字符验证
     */
    private Integer codeType;

    /**
     * 字符验证码位数
     */
    private Integer charCodeLength;

    /**
     * 数字验证码位数
     */
    private Integer numberCodeLength;

    /**
     * 线段干扰 0 线段干扰 1 圆圈干扰 2扭曲干扰
     */
    private Integer codeCategory;

    /**
     * 登录提示语状态 【Y:正常；N:关闭】
     */
    private String loginMsgStatus;

    /**
     * 登录提示语信息
     */
    private String loginMsgInfo;

    /**
     * 白名单验证状态 【Y:正常；N:关闭】
     */
    private String whitelistVerifyStatus;

    /**
     * 白名单设置
     */
    private String whitelistSetting;

    /**
     * 密码定时更新状态 【Y:正常；N:关闭】
     */
    private String passwordUpdateStatus;

    /**
     * 更新周期
     */
    private Integer updateCycle;

    /**
     * 提前提醒更新
     */
    private Integer remindUpdate;

    /**
     * 密码定时更新状态 【Y:正常；N:关闭】
     */
    private String passwordStrengthLimitStatus;

    /**
     * 最小长度状态 【Y:正常；N:关闭】
     */
    private String minLengthStatus;

    /**
     * 最小长度 【Y:正常；N:关闭】
     */
    private String minLength;

    /**
     * 密码包含数字状态 【Y:正常；N:关闭】
     */
    private String containsNumberStatus;

    /**
     * 密码包含小写字母状态 【Y:正常；N:关闭】
     */
    private String containsLowercaseLettersStatus;

    /**
     * 密码包含大写字母状态 【Y:正常；N:关闭】
     */
    private String containsCapitalLettersStatus;

    /**
     * 密码包含字母状态 【Y:正常；N:关闭】
     */
    private String containsLettersStatus;

    /**
     * 默认密码
     */
    private String defaultPassword;


}
