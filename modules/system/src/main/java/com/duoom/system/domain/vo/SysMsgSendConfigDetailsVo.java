package com.duoom.system.domain.vo;

import com.duoom.common.translation.annotation.Translation;
import com.duoom.common.translation.constant.TransConstant;
import com.duoom.system.domain.SysMsgSendConfigDetails;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 消息中心-发送配置-消息配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@AutoMapper(target = SysMsgSendConfigDetails.class)
public class SysMsgSendConfigDetailsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 消息类型【0：站内信；1：邮件；2：短信；3：钉钉；4：企业微信；5：webhook；6：微信公众号】
     */
    private Integer msgType;

    /**
     * 消息模板 id
     */
    private Long templateId;

    /**
     * 消息模板名称
     */
    @Translation(type = TransConstant.TEMP_ID_TO_NAME, mapper = "templateId")
    private String templateName;

    /**
     * 消息模板编码
     */
    @Translation(type = TransConstant.TEMP_ID_TO_CODE, mapper = "templateId")
    private String templateCode;

    /**
     * 账号模板 id （邮件或短信 id）
     */
    private Long accountId;

    /**
     * 账号模板名称
     */
    private String accountName;

    /**
     * 账号模板编码
     */
    private String accountCode;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态【Y：启用；N：禁用】
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
