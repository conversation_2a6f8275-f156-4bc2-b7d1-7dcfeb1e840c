package com.duoom.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.duoom.common.core.constant.SystemConstants;
import com.duoom.common.mybatis.annotation.DataColumn;
import com.duoom.common.mybatis.annotation.DataPermission;
import com.duoom.common.mybatis.core.mapper.BaseMapperPlus;
import com.duoom.common.mybatis.helper.DataBaseHelper;
import com.duoom.system.domain.SysPost;
import com.duoom.system.domain.vo.SysPostVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 岗位信息 数据层
 *
 * <AUTHOR>
 */
public interface SysPostMapper extends BaseMapperPlus<SysPost, SysPostVo> {

    /**
     * 分页查询岗位列表
     *
     * @param page         分页对象
     * @param queryWrapper 查询条件
     * @return 包含岗位信息的分页结果
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<SysPostVo> selectPagePostList(@Param("page") Page<SysPostVo> page, @Param(Constants.WRAPPER) Wrapper<SysPost> queryWrapper);

    /**
     * 查询用户所属岗位组
     *
     * @param userId 用户ID
     * @return 结果
     */
    List<SysPostVo> selectPostsByUserId(Long userId);

    /**
     * 统计指定岗位分类ID的分类数量
     *
     * @param postId 岗位分类ID
     * @return 该岗位分类ID的分类数量
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id")
    })
    default long countPostById(Long postId){
         return selectCount(new LambdaQueryWrapper<SysPost>()
             .eq(SysPost::getPostId, postId)
             .eq( SysPost::getDelFlag, SystemConstants.NORMAL));
    }

    /**
     * 根据父岗位分类ID查询其所有子岗位分类的列表
     *
     * @param parentId 父岗位分类ID
     * @return 包含子岗位分类的列表
     */
    default List<SysPost> selectListByParentId(Long parentId) {
        return this.selectList(new LambdaQueryWrapper<SysPost>()
            .select(SysPost::getPostId)
            .apply(DataBaseHelper.findInSet(parentId, "ancestors")));
    }

    /**
     * 根据父岗位分类ID查询包括父ID及其所有子岗位分类ID的列表
     *
     * @param parentId 父岗位分类ID
     * @return 包含父ID和子岗位分类ID的列表
     */
    default List<Long> selectPostIdsByParentId(Long parentId) {
        return Stream.concat(
            this.selectListByParentId(parentId).stream()
                .map(SysPost::getPostId),
            Stream.of(parentId)
        ).collect(Collectors.toList());
    }
}
