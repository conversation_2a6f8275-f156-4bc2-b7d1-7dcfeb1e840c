package com.duoom.system.domain.vo;

import com.duoom.system.domain.SysMenuColumn;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 菜单列字段视图对象 sys_menu_column
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@AutoMapper(target = SysMenuColumn.class)
public class SysMenuColumnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 菜单主键
     */
    private Long menuId;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;


}
