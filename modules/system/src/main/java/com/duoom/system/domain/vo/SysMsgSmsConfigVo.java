package com.duoom.system.domain.vo;

import com.duoom.system.domain.SysMsgSmsConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 消息中心-短信配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@AutoMapper(target = SysMsgSmsConfig.class)
public class SysMsgSmsConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 渠道【0：阿里云；1：腾讯云】
     */
    private Integer channel;

    /**
     * 短信签名
     */
    private String signature;

    /**
     * Access Key Id
     */
    private String accessKeyId;

    /**
     * Access Key Secret
     */
    private String accessKeySecret;

    /**
     * Sdk App Id（阿里云非必填）
     */
    private String sdkAppId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态【Y：启用；N：禁用】
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
