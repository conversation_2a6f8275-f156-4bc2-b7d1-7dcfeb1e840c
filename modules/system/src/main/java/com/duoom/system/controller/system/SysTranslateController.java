package com.duoom.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.duoom.common.core.domain.R;
import com.duoom.common.core.service.I18nService;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.system.domain.bo.SysTranslateBo;
import com.duoom.system.domain.vo.SysTranslateVo;
import com.duoom.system.service.ISysTranslateService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Map;

/**
 * I18n 翻译管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/translation")
public class SysTranslateController extends BaseController {

    private final ISysTranslateService sysTranslateService;
    private final I18nService i18nService;

    /**
     * I18n 翻译内容加载
     * 系统启动时需要调用，不能限制登录
     * @param type 客户端类型
     * @param lang 语言
     * @return 翻译内容
     */
    @SaIgnore
    @GetMapping("/i18n/load")
    public R<Map<String,Object>> load(@NotBlank(message = "客户端类型不能为空") String type,
                                      @NotBlank(message = "翻译语言不能为空")String lang) {
        return R.ok(i18nService.getMessagesByLocale(type,lang.trim()));
    }

    /**
     * I18n 翻译管理-列表
     */
    @Log(title = "I18n 翻译管理", businessType = BusinessType.SELECT)
    @SaCheckPermission("system:trans:query")
    @GetMapping("/list")
    public TableDataInfo<SysTranslateVo> list(SysTranslateBo bo, PageQuery pageQuery) {
        return sysTranslateService.queryPageList(bo, pageQuery);
    }

    /**
     * I18n 翻译管理-详情
     */
    @SaCheckPermission("system:trans:query")
    @Log(title = "I18n 翻译管理", businessType = BusinessType.SELECT)
    @RepeatSubmit()
    @GetMapping("/{id}")
    public R<SysTranslateVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(sysTranslateService.getById(id));
    }

    /**
     * I18n 翻译管理-新增
     */
    @SaCheckPermission("system:trans:add")
    @Log(title = "I18n 翻译管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysTranslateBo bo) {
        return toAjax(sysTranslateService.insertByBo(bo));
    }

    /**
     * I18n 翻译管理-修改
     */
    @SaCheckPermission("system:trans:edit")
    @Log(title = "I18n 翻译管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysTranslateBo bo) {
        return toAjax(sysTranslateService.updateByBo(bo));
    }

    /**
     * I18n 翻译管理-删除
     */
    @SaCheckPermission("system:trans:remove")
    @Log(title = "I18n 翻译管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sysTranslateService.remove(Arrays.asList(ids)));
    }

    /**
     * I18n 翻译管理-导出（导入前先导出）
     */
    @Log(title = "I18n 翻译管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:trans:export")
    @PostMapping("/export")
    public void export(SysTranslateBo bo, HttpServletResponse response) {
        sysTranslateService.export(bo, response);
    }


    /**
     * I18n 翻译管理-导入
     *
     * @param file 导入文件
     */
    @Log(title = "I18n 翻译管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:trans:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file) {
        sysTranslateService.importData(file);
        return R.ok();
    }

}
