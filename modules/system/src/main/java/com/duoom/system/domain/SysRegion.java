package com.duoom.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.duoom.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 地区表 sys_region
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_region")
public class SysRegion extends BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 层级【1:省 2:市 3:区 4:街】
     */
    private Integer level;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 首字母
     */
    private String first;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 完整id
     */
    private String extId;

    /**
     * 名称
     */
    private String name;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 状态（Y正常 N禁用）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;

}
