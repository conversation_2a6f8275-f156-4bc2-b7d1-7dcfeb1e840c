package com.duoom.system.domain.bo;

import com.duoom.system.domain.SysInfoConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 *  系统信息配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@AutoMapper(target = SysInfoConfig.class)
public class SysInfoConfigBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 公司名称
     */
    @Size(min = 0, max = 100, message = "公司名称长度不能超过{max}个字符")
    private String companyName;

    /**
     * 公司简称
     */
    @Size(min = 0, max = 100, message = "公司简称长度不能超过{max}个字符")
    private String companyShortName;

    /**
     * 刷新 logo (ossId)
     */
    private String refreshIcon;

    /**
     * 登录页 logo (ossId)
     */
    private String loginIcon;

    /**
     * 菜单页 logo (ossId)
     */
    private String menuIcon;

    /**
     * 设计器 logo (ossId)
     */
    private String designIcon;

    /**
     * 登录页背景 (ossId)
     */
    private String loginBackground;

}
