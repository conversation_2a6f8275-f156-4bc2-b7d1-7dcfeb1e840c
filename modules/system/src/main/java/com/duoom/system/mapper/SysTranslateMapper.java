package com.duoom.system.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.duoom.common.mybatis.core.mapper.BaseMapperPlus;
import com.duoom.system.domain.SysTranslate;
import com.duoom.system.domain.vo.I18nMessage;
import com.duoom.system.domain.vo.SysTranslateVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基本设置Mapper接口
 */
public interface SysTranslateMapper extends BaseMapperPlus<SysTranslate, SysTranslateVo> {

    /**
     * 根据 code查询
     * @param codes codes
     * @return Map<code, id>
     */
    default Map<String, Long> selectIdCodeByCodesMap(List<String> codes) {
        List<SysTranslate> list = selectList(new LambdaQueryWrapper<SysTranslate>().in(SysTranslate::getCode, codes).select(SysTranslate::getId, SysTranslate::getCode));
        if (CollUtil.isEmpty(list)) {
            return MapUtil.newHashMap();
        }
        return list.stream().collect(Collectors.toMap(SysTranslate::getCode, SysTranslate::getId));
    }

    /**
     * 根据语言查询
     * @param locale 语言
     * @param type 客户端类型
     * @return Map<code, translation>
     */
    List<I18nMessage> selectCodeTranslationMap(@Param("type") String type, @Param("locale") String locale);

}
