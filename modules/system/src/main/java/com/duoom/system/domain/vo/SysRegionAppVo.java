package com.duoom.system.domain.vo;

import com.duoom.common.core.domain.base.BaseSelectVo;
import com.duoom.system.domain.SysRegion;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 地区表 sys_region
 *
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = SysRegionVo.class)
public class SysRegionAppVo extends BaseSelectVo {

    /**
     * 父id
     */
    @JsonIgnore
    private Long parentId;

    /**
     * 排序
     */
    @JsonIgnore
    private Integer sort;

    /**
     * 子集
     */
    private List<SysRegionAppVo> vos;

}
