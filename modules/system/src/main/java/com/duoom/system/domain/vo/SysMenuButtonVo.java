package com.duoom.system.domain.vo;

import com.duoom.system.domain.SysMenuButton;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 菜单按钮视图对象 sys_menu_button
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@AutoMapper(target = SysMenuButton.class)
public class SysMenuButtonVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 按钮名
     */
    private String name;

    /**
     * 菜单Id
     */
    private Long menuId;

    /**
     * 图标
     */
    private String icon;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求方式(0 GET,1 POST,2 PUT,3 DELETE)
     */
    private Long method;

    /**
     * 备注
     */
    private String remark;


}
