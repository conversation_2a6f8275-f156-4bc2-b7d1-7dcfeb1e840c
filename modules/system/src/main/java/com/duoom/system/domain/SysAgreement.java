package com.duoom.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 协议信息对象 agreement
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_agreement")
public class SysAgreement extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 协议ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 协议标题
     */
    private String title;

    /**
     * 协议类型（字典类型：sys_agreement_type）
     */
    private String agremType;

    /**
     * 协议内容
     */
    private String content;

    /**
     * 协议状态【Y:正常；N:关闭】
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
