package com.duoom.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.duoom.common.core.domain.R;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.system.domain.SysVersion;
import com.duoom.system.domain.bo.SysVersionBo;
import com.duoom.system.domain.vo.SysVersionVo;
import com.duoom.system.service.ISysVersionService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 版本管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/version")
public class SysVersionController extends BaseController {

    private final ISysVersionService sysVersionService;

    /**
     * 版本管理-列表
     */
    @SaCheckPermission("system:version:list")
    @GetMapping("/list")
    public TableDataInfo<SysVersionVo> list(PageQuery pageQuery) {
        return sysVersionService.queryPageList(pageQuery);
    }

    /**
     * 版本管理-新增
     */
    @SaCheckPermission("system:version:add")
    @Log(title = "版本管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysVersionBo bo) {
        return toAjax(sysVersionService.insertByBo(bo));
    }

    /**
     * 版本管理-修改
     */
    @SaCheckPermission("system:version:edit")
    @Log(title = "版本管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysVersionBo bo) {
        return toAjax(sysVersionService.updateByBo(bo));
    }

    /**
     * 版本管理-删除
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:version:remove")
    @Log(title = "版本管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sysVersionService.remove(Arrays.asList(ids)));
    }

    /**
     * 版本管理-根据更新类型获取最新版本信息
     *
     * @param updateType 更新类型
     */
    @SaCheckPermission("system:version:query")
    @GetMapping("/query")
    public R<SysVersionVo> query (@RequestParam("updateType") String updateType){
        return R.ok(sysVersionService.query(updateType));
    }
}
