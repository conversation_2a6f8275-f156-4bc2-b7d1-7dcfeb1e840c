package com.duoom.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.duoom.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Map;


/**
 * I18n 翻译管理对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_translate",autoResultMap = true)
public class SysTranslate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 翻译分类【0：客户端；1：服务端】
     */
    private String translateType;

    /**
     * 编码
     */
    private String code;

    /**
     * 翻译内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, String> content;

}
