package com.duoom.system.controller.system;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.duoom.common.core.domain.R;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.system.domain.bo.SysMsgSmsConfigBo;
import com.duoom.system.domain.bo.SysMsgSmsConfigQueryBo;
import com.duoom.system.domain.vo.SysMsgSmsConfigListVo;
import com.duoom.system.domain.vo.SysMsgSmsConfigVo;
import com.duoom.system.service.ISysMsgSmsConfigService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 消息中心-短信配置
 *
 * <AUTHOR>
 * @since 2025-05-21
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/msg/sms/config")
public class SysMsgSmsConfigController extends BaseController {

    private final ISysMsgSmsConfigService sysMsgSmsConfigService;

    /**
     * 短信配置-新增
     */
    @SaCheckPermission("system:smsConfig:add")
    @Log(title = "短信配置", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysMsgSmsConfigBo bo) {
        return toAjax(sysMsgSmsConfigService.add(bo));
    }

    /**
     * 短信配置-修改
     */
    @SaCheckPermission("system:smsConfig:edit")
    @Log(title = "短信配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/update")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysMsgSmsConfigBo bo) {
        return toAjax(sysMsgSmsConfigService.update(bo));
    }

    /**
     * 短信配置-删除
     *
     * @param id id
     */
    @SaCheckPermission("system:smsConfig:remove")
    @Log(title = "短信配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public R<Void> remove(@NotNull(message = "主键不能为空") @RequestParam("id") Long id) {
        return toAjax(sysMsgSmsConfigService.remove(id));
    }

    /**
     * 短信配置-回显
     */
    @Log(title = "短信配置", businessType = BusinessType.SELECT)
    @SaCheckPermission("system:smsConfig:query")
    @GetMapping("/{id}")
    public R<SysMsgSmsConfigVo> query(@PathVariable Long id) {
        return R.ok(sysMsgSmsConfigService.query(id));
    }

    /**
     * 短信配置-列表
     */
    @Log(title = "短信配置", businessType = BusinessType.SELECT)
    @SaCheckPermission("system:smsConfig:list")
    @GetMapping("/list")
    public TableDataInfo<SysMsgSmsConfigListVo> list(SysMsgSmsConfigQueryBo bo, PageQuery pageQuery) {
        return sysMsgSmsConfigService.list(bo, pageQuery);
    }

}
