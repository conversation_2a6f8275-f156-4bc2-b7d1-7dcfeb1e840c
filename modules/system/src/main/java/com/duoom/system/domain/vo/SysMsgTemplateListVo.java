package com.duoom.system.domain.vo;

import com.duoom.system.domain.SysMsgTemplate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 消息中心-消息模板-列表响应类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysMsgTemplate.class)
public class SysMsgTemplateListVo extends SysMsgBaseVo {

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 消息来源【0：公告；1：流程；2：系统；3：日程】
     */
    private Integer msgSource;

    /**
     * 消息类型【0：站内信；1：邮件；2：短信；3：钉钉；4：企业微信；5：webhook；6：微信公众号】
     */
    private Integer msgType;


}
