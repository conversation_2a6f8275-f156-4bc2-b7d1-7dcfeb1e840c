package com.duoom.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.duoom.common.core.constant.CacheNames;
import com.duoom.common.core.domain.model.LoginUser;
import com.duoom.common.core.enums.StatusEnum;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.core.utils.MapstructUtils;
import com.duoom.common.core.utils.StreamUtils;
import com.duoom.common.mail.utils.MailUtils;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.redis.utils.RedisUtils;
import com.duoom.common.satoken.utils.LoginHelper;
import com.duoom.common.sms.config.ReadConfig;
import com.duoom.common.sse.dto.SseMessageDto;
import com.duoom.common.sse.utils.SseMessageUtils;
import com.duoom.system.domain.*;
import com.duoom.system.domain.bo.SysMsgSendBo;
import com.duoom.system.domain.bo.SysMsgSendConfigAddBo;
import com.duoom.system.domain.bo.SysMsgSendConfigEditBo;
import com.duoom.system.domain.bo.SysMsgSendConfigQueryBo;
import com.duoom.system.domain.vo.*;
import com.duoom.system.enums.SysMsgTypeEnum;
import com.duoom.system.mapper.*;
import com.duoom.system.service.ISysMsgSendConfigService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 消息中心-发送配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysMsgSendConfigServiceImpl implements ISysMsgSendConfigService {

    private final SysMsgSendConfigMapper sysMsgSendConfigMapper;

    private final SysMsgSendConfigDetailsMapper sysMsgSendConfigDetailsMapper;

    private final SysMsgSmsConfigMapper sysMsgSmsConfigMapper;

    private final SysMsgMailConfigMapper sysMsgMailConfigMapper;

    private final ReadConfig readConfig;

    private final SysUserMapper sysUserMapper;

    private final SysMsgTemplateMapper sysMsgTemplateMapper;

    private final SysMsgInternalMapper sysMsgInternalMapper;

    private final SysMsgInternalUserMapper sysMsgInternalUserMapper;

    private final SysMsgMonitoringMapper sysMsgMonitoringMapper;

    /**
     * 发送配置-新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer add(SysMsgSendConfigAddBo bo) {

        verifyCodeUnique(bo.getCode(), null);

        SysMsgSendConfig sysMsgSendConfig = MapstructUtils.convert(bo, SysMsgSendConfig.class);

        int insert = sysMsgSendConfigMapper.insert(sysMsgSendConfig);

        if (insert > 0) {

            bo.getBos().forEach(details -> details.setSendConfigId(sysMsgSendConfig.getId()));
            List<SysMsgSendConfigDetails> sysMsgSendConfigDetails = MapstructUtils.convert
                (bo.getBos(), SysMsgSendConfigDetails.class);

            boolean insertBatch = sysMsgSendConfigDetailsMapper.insertBatch(sysMsgSendConfigDetails);

            if (!insertBatch) {
                throw new ServiceException("操作失败");
            }
        }

        return insert;
    }

    /**
     * 发送配置-修改
     * 处理逻辑：子数据先删再增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(SysMsgSendConfigEditBo bo) {

        verifyCodeUnique(bo.getCode(), bo.getId());
        SysMsgSendConfig sysMsgSendConfig = MapstructUtils.convert(bo, SysMsgSendConfig.class);

        int update = sysMsgSendConfigMapper.updateById(sysMsgSendConfig);

        if (update > 0) {

            int delete = sysMsgSendConfigDetailsMapper
                .delete(
                    new LambdaQueryWrapper<SysMsgSendConfigDetails>()
                        .eq(SysMsgSendConfigDetails::getSendConfigId, bo.getId())
                );

            if (delete <= 0) {
                throw new ServiceException("删除消息配置失败");
            }

            bo.getBos().forEach(details -> details.setSendConfigId(bo.getId()));
            List<SysMsgSendConfigDetails> sysMsgSendConfigDetails = MapstructUtils.convert
                (bo.getBos(), SysMsgSendConfigDetails.class);

            boolean insertBatch = sysMsgSendConfigDetailsMapper.insertBatch(sysMsgSendConfigDetails);

            if (!insertBatch) {
                throw new ServiceException("新增消息配置失败");
            }
        }

        return update;
    }

    /**
     * 发送配置-删除
     *
     * @param id id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer remove(Long id) {

        int delete = sysMsgSendConfigMapper.deleteById(id);
        int deleteDetails = sysMsgSendConfigDetailsMapper
            .delete(
                new LambdaQueryWrapper<SysMsgSendConfigDetails>()
                    .eq(SysMsgSendConfigDetails::getSendConfigId, id)
            );

        if (delete <= 0 || deleteDetails <= 0) {
            throw new ServiceException("操作失败");
        }

        return delete;
    }


    /**
     * 发送配置-回显
     *
     * @param id id
     */
    @Override
    public SysMsgSendConfigVo query(Long id) {

        SysMsgSendConfigVo vo = sysMsgSendConfigMapper.selectVoById(id);

        List<SysMsgSendConfigDetailsVo> vos = sysMsgSendConfigDetailsMapper
            .selectVoList(new LambdaQueryWrapper<SysMsgSendConfigDetails>()
                .eq(SysMsgSendConfigDetails::getSendConfigId, id)
                .orderByAsc(SysMsgSendConfigDetails::getSort));

        dataTreating(vos);

        vo.setVos(vos);

        return vo;
    }

    /**
     * 发送配置-子数据处理
     * 插入配置详情账号名称与编码
     */
    private void dataTreating(List<SysMsgSendConfigDetailsVo> vos) {

        // key：消息类型, value：账号 ids
        Map<Integer, List<Long>> collect =
            vos.stream()
                .filter(vo -> ObjUtil.isNotNull(vo.getAccountId()))
                .collect(
                    Collectors.groupingBy(
                        SysMsgSendConfigDetailsVo::getMsgType,
                        Collectors.mapping(SysMsgSendConfigDetailsVo::getAccountId, Collectors.toList())
                    )
                );

        // Map<消息类型，Map<账号模板 id，账号名称与编码>>
        Map<Integer, Map<Long, SysMsgAccountVo>> msgTypeAccountMap = new HashMap<>();

        // 处理不同消息类型
        Arrays.stream(SysMsgTypeEnum.values()).forEach(msgTypeEnum -> {

            List<Long> configIds = collect.get(msgTypeEnum.getMsgType());

            if (CollectionUtil.isNotEmpty(configIds)) {
                msgTypeAccountMap.put(
                    msgTypeEnum.getMsgType(),
                    byMsgTypeGetAccount(msgTypeEnum.getMsgType(), configIds)
                );
            }
        });

        // 处理每个 SysMsgSendConfigDetailsVo
        for (SysMsgSendConfigDetailsVo vo : vos) {
            Integer msgType = vo.getMsgType();
            SysMsgAccountVo sysMsgAccountVo = msgTypeAccountMap.getOrDefault(msgType, new HashMap<>()).get(vo.getAccountId());

            if (ObjUtil.isNotNull(sysMsgAccountVo)) {
                vo.setAccountName(sysMsgAccountVo.getName());
                vo.setAccountCode(sysMsgAccountVo.getCode());
            }
        }
    }


    /**
     * 根据消息类型获取账号名称与编码
     *
     * @param msgType 消息类型【0：站内信；1：邮件；2：短信；3：钉钉；4：企业微信；5：webhook；6：微信公众号】
     * @param ids     ids 账号模板 ids
     *                <p>
     *                目前版本消息类型仅支持【0：站内信；1：邮件；2：短信；】
     *                方法已全部定义，后续新增类型时，在对应方法上写具体实现即可。
     *                <p>
     * @return key：账号模板 id （邮件配置或短信配置 id）；value：名称与编码
     */
    private Map<Long, SysMsgAccountVo> byMsgTypeGetAccount(Integer msgType, Collection<Long> ids) {

        Map<Integer, MsgTypeHandler> handlerMap = new HashMap<>();

        handlerMap.put(SysMsgTypeEnum.INTERNAL.getMsgType(), this::handleInternalMessage);
        handlerMap.put(SysMsgTypeEnum.MAIL.getMsgType(), this::handleMailMessage);
        handlerMap.put(SysMsgTypeEnum.SMS.getMsgType(), this::handleSmsMessage);
        handlerMap.put(SysMsgTypeEnum.DING_TALK.getMsgType(), this::handleDingTalkMessage);
        handlerMap.put(SysMsgTypeEnum.ENTERPRISE_WECHAT.getMsgType(), this::handleEnterpriseWechatMessage);
        handlerMap.put(SysMsgTypeEnum.WEBHOOK.getMsgType(), this::handleWebhookMessage);
        handlerMap.put(SysMsgTypeEnum.WECHAT_OFFICIAL_ACCOUNT.getMsgType(), this::handleWechatOfficialAccountMessage);

        MsgTypeHandler handler = handlerMap.get(msgType);
        if (ObjUtil.isNotNull(handler)) {
            return listToMap(handler.handle(ids));
        } else {
            return new HashMap<>();
        }
    }

    /**
     * List<SysMsgAccountDto> 转 map
     */
    private Map<Long, SysMsgAccountVo> listToMap(List<SysMsgAccountVo> sysMsgAccountVoList) {

        if (CollectionUtil.isNotEmpty(sysMsgAccountVoList)) {
            return sysMsgAccountVoList
                .stream()
                .collect(Collectors.toMap(SysMsgAccountVo::getId, SysMsgAccountVo -> SysMsgAccountVo));
        } else {
            return new HashMap<>();
        }
    }

    /**
     * 消息类型处理策略接口
     */
    @FunctionalInterface
    interface MsgTypeHandler {
        List<SysMsgAccountVo> handle(Collection<Long> ids);
    }

    /**
     * 站内信
     */
    private List<SysMsgAccountVo> handleInternalMessage(Collection<Long> ids) {
        // 站内信无账号
        return null;
    }

    /**
     * 邮件
     */
    private List<SysMsgAccountVo> handleMailMessage(Collection<Long> ids) {
        return selectAccountsByIds(sysMsgMailConfigMapper, ids);
    }

    /**
     * 短信
     */
    private List<SysMsgAccountVo> handleSmsMessage(Collection<Long> ids) {
        return selectAccountsByIds(sysMsgSmsConfigMapper, ids);
    }

    /**
     * 钉钉
     */
    private List<SysMsgAccountVo> handleDingTalkMessage(Collection<Long> ids) {
        return null;
    }

    /**
     * 企业微信
     */
    private List<SysMsgAccountVo> handleEnterpriseWechatMessage(Collection<Long> ids) {
        return null;
    }

    /**
     * Webhook
     */
    private List<SysMsgAccountVo> handleWebhookMessage(Collection<Long> ids) {
        return null;
    }

    /**
     * 微信公众号
     */
    private List<SysMsgAccountVo> handleWechatOfficialAccountMessage(Collection<Long> ids) {
        return null;
    }


    /**
     * 根据账号模板 id 查询出对应账号名称与编码
     *
     * @param mapper 各账号模板 dao 层实例
     * @param ids    账号模板 ids
     * @return 账号模板数据
     */
    private <T> List<SysMsgAccountVo> selectAccountsByIds(BaseMapper<T> mapper, Collection<Long> ids) {

        List<T> sysMsgAccountVos = mapper.selectList(
            new QueryWrapper<T>()
                .select("id", "name", "code")
                .in("id", ids)
        );

        if (CollUtil.isNotEmpty(sysMsgAccountVos)) {
            return BeanUtil.copyToList(sysMsgAccountVos, SysMsgAccountVo.class);
        } else {
            return new ArrayList<>();
        }
    }


    /**
     * 发送配置-列表
     */
    @Override
    public TableDataInfo<SysMsgSendConfigListVo> list(SysMsgSendConfigQueryBo bo, PageQuery pageQuery) {

        LambdaQueryWrapper<SysMsgSendConfig> wrapper = wrapper(bo);

        IPage<SysMsgSendConfigListVo> page = sysMsgSendConfigMapper
            .selectVoPage(pageQuery.build(), wrapper, SysMsgSendConfigListVo.class);

        setMsgType(page.getRecords());

        return TableDataInfo.build(page);
    }


    /**
     * 发送配置 - 列表
     * 处理消息类型
     * 一条列表数据可对应多个消息类型
     */
    private void setMsgType(List<SysMsgSendConfigListVo> vos) {

        List<Long> sendIds = vos.stream().map(SysMsgSendConfigListVo::getId).toList();

        List<SysMsgSendConfigDetails> detailsList =
            sysMsgSendConfigDetailsMapper
                .selectList(new LambdaQueryWrapper<SysMsgSendConfigDetails>()
                    .select(SysMsgSendConfigDetails::getSendConfigId, SysMsgSendConfigDetails::getMsgType)
                    .in(SysMsgSendConfigDetails::getSendConfigId, sendIds));


        if (CollectionUtil.isNotEmpty(detailsList)) {

            // key：发送配置 id，value：消息类型
            Map<Long, Set<Integer>> collect = detailsList.stream().collect(
                Collectors.groupingBy(
                    SysMsgSendConfigDetails::getSendConfigId,
                    Collectors.mapping(SysMsgSendConfigDetails::getMsgType, Collectors.toSet())
                )
            );

            vos.forEach(vo -> vo.setMsgTypes(collect.get(vo.getId())));
        }
    }

    /**
     * 发送配置-列表
     * wrapper 条件
     */
    private LambdaQueryWrapper<SysMsgSendConfig> wrapper(SysMsgSendConfigQueryBo bo) {

        LambdaQueryWrapper<SysMsgSendConfig> wrapper = new LambdaQueryWrapper<>();

        wrapper.select
                (
                    SysMsgSendConfig::getId, SysMsgSendConfig::getName, SysMsgSendConfig::getCode,
                    SysMsgSendConfig::getMsgSource, SysMsgSendConfig::getCreateBy,
                    SysMsgSendConfig::getCreateTime, SysMsgSendConfig::getUpdateTime,
                    SysMsgSendConfig::getSort, SysMsgSendConfig::getStatus
                )

            .eq(ObjUtil.isNotNull(bo.getMsgSource()), SysMsgSendConfig::getMsgSource, bo.getMsgSource())
            .eq(StrUtil.isNotBlank(bo.getStatus()), SysMsgSendConfig::getStatus, bo.getStatus())

            .and(StrUtil.isNotBlank(bo.getSearchValue()), search -> search
                .like(SysMsgSendConfig::getName, bo.getSearchValue())
                .or()
                .like(SysMsgSendConfig::getCode, bo.getSearchValue()));

        return wrapper;
    }

    /**
     * 发送配置-消息发送列表
     *
     * @param id 发送配置 id
     */
    @Override
    public List<SysMsgSendVo> sendVo(Long id) {
        return sysMsgSendConfigDetailsMapper.sendVo(id);
    }

    /**
     * 发送配置-消息发送
     */
    @Override
    public List<SysMsgSendStatusVo> send(List<SysMsgSendBo> bos) {

        // 注册消息实例
        registerMsg(bos);

        // 发送消息
        return sendMsg(bos);

    }

    /**
     * 注册消息实例
     */
    private void registerMsg(List<SysMsgSendBo> bos) {

        // key：消息类型, value：账号 ids
        Map<Integer, List<Long>> accounts =
            bos.stream()
                .filter(bo -> ObjUtil.isNotNull(bo.getAccountId()))
                .collect(
                    Collectors.groupingBy(
                        SysMsgSendBo::getMsgType,
                        Collectors.mapping(SysMsgSendBo::getAccountId, Collectors.toList())
                    )
                );

        // 注册消息实例
        registerMsg(accounts, SysMsgTypeEnum.SMS.getMsgType(), sysMsgSmsConfigMapper::selectVoByIds, CacheNames.SMS);
        registerMsg(accounts, SysMsgTypeEnum.MAIL.getMsgType(), sysMsgMailConfigMapper::selectVoByIds, CacheNames.MAIL);

    }

    /**
     * 注册消息实例，并将数据缓存到 redis
     *
     * @param accounts       key：消息类型, value：账号 ids
     * @param msgType        消息类型【0：站内信；1：邮件；2：短信；3：钉钉；4：企业微信；5：webhook；6：微信公众号】
     * @param selectFunction 执行的方法
     * @param cacheName      redis Key
     */
    private <T> void registerMsg(Map<Integer, List<Long>> accounts, Integer msgType,
                                 Function<List<Long>, List<T>> selectFunction, String cacheName) {

        // 账号 ids
        List<Long> ids = accounts.get(msgType);

        if (CollUtil.isNotEmpty(ids)) {

            // 账号数据
            List<T> vos = selectFunction.apply(ids);

            if (CollUtil.isNotEmpty(vos)) {

                // key：账号 id ；value：账号数据
                Map<String, T> accountMap =
                    vos.stream()
                        .collect(Collectors.toMap(vo -> String.valueOf(getId(vo)), vo -> vo));

                RedisUtils.setCacheMap(cacheName, accountMap);

                accountMap.keySet().forEach(accountId -> {

                    // 注册（只有短信才需注册到 SmsFactory,其他消息缓存到 redis）
                    if (CacheNames.SMS.equals(cacheName)) {
                        SmsFactory.createSmsBlend(readConfig, accountId);
                    }
                });

            }
        }
    }

    /**
     * 获取对应实体类型字段
     * 后续新增消息账号，在下方添加实体类即可。
     *
     * @param vo 实体类
     * @return 实体类 id
     */
    private <T> Long getId(T vo) {

        if (vo instanceof SysMsgSmsConfigVo) {
            return ((SysMsgSmsConfigVo) vo).getId();
        } else if (vo instanceof SysMsgMailConfigVo) {
            return ((SysMsgMailConfigVo) vo).getId();
        }

        throw new ServiceException("未找到对应账号类型");
    }

    /**
     * 发送消息
     */
    private List<SysMsgSendStatusVo> sendMsg(List<SysMsgSendBo> bos) {

        // 用户数据 key：userId；value：user（注：这是所有发送配置的所有人）
        Map<Long, SysUserVo> userVoMap = getUserVoMap(bos);

        // 消息模板 key：消息模板id；value：消息模板（注：这是所有发送配置的所有模板）
        Map<Long, SysMsgTemplateVo> templateVoMap = getTemplateVoMap(bos);

        List<SysMsgSendStatusVo> vos = new ArrayList<>();

        LoginUser loginUser = LoginHelper.getLoginUser();

        // 每个 bo 为一条发送配置
        for (SysMsgSendBo bo : bos) {

            // 发送短信
            if (SysMsgTypeEnum.SMS.getMsgType().equals(bo.getMsgType())) {
                vos.add(sendSms(bo, userVoMap, templateVoMap, loginUser));
            }
            // 发送邮件
            else if (SysMsgTypeEnum.MAIL.getMsgType().equals(bo.getMsgType())) {
                vos.add(sendMail(bo, userVoMap, templateVoMap, loginUser));
            }
            // 发送站内信
            else if (SysMsgTypeEnum.INTERNAL.getMsgType().equals(bo.getMsgType())) {
                vos.add(sendInternal(bo, userVoMap, templateVoMap, loginUser));
            }

        }

        return vos;

    }

    /**
     * 获取用户数据
     * key：userId；value：user
     */
    private Map<Long, SysUserVo> getUserVoMap(List<SysMsgSendBo> bos) {

        Set<Long> userIds = bos.stream().flatMap(bo -> bo.getUserIds().stream()).collect(Collectors.toSet());
        List<SysUserVo> sysUserVos = sysUserMapper.selectUserList(
            new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getUserId, SysUser::getNickName, SysUser::getPhonenumber, SysUser::getEmail)
                .in(SysUser::getUserId, userIds)
        );

        return StreamUtils.toMap(sysUserVos, SysUserVo::getUserId, user -> user);
    }


    /**
     * 获取消息模板
     * key：消息模板id；value：消息模板
     */
    private Map<Long, SysMsgTemplateVo> getTemplateVoMap(List<SysMsgSendBo> bos) {

        List<String> templateIds = StreamUtils.toList(bos, SysMsgSendBo::getId);

        List<SysMsgTemplateVo> sysMsgTemplateVos = sysMsgTemplateMapper.selectVoList(
            new LambdaQueryWrapper<SysMsgTemplate>()
                .select(SysMsgTemplate::getId, SysMsgTemplate::getTemplateId, SysMsgTemplate::getMsgTitle,
                    SysMsgTemplate::getMsgContent, SysMsgTemplate::getMsgType, SysMsgTemplate::getParameterJson,
                    SysMsgTemplate::getMsgSource)
                .in(SysMsgTemplate::getId, templateIds)
        );

        return StreamUtils.toMap(sysMsgTemplateVos, SysMsgTemplateVo::getId, template -> template);
    }

    /**
     * 发送短信
     *
     * @param bo            发送配置（根据 id 获取对应人与模板）
     * @param userVoMap     所有发送配置的接收人 <key：用户id, value：用户数据>
     * @param templateVoMap 所有发送配置的消息模板 <key：消息模板id, value：模板数据>
     * @param loginUser     当前登陆人（消息发送者）
     */
    private SysMsgSendStatusVo sendSms(SysMsgSendBo bo, Map<Long, SysUserVo> userVoMap, Map<Long, SysMsgTemplateVo> templateVoMap, LoginUser loginUser) {

        // 消息监控数据 - 接收人
        List<String> users = new ArrayList<>();

        // 当前发送配置需要发送的手机号
        List<String> phones = new ArrayList<>();
        bo.getUserIds().forEach(user -> {
            SysUserVo sysUserVo = userVoMap.get(user);
            phones.add(sysUserVo.getPhonenumber());
            users.add(sysUserVo.getNickName());
        });

        // 当前发送配置需要发送的内容
        SysMsgTemplateVo sysMsgTemplateVo = templateVoMap.get(Long.valueOf(bo.getId()));
        String templateId = sysMsgTemplateVo.getTemplateId(); // 短信模板 id
        LinkedHashMap<String, String> resultMap; // 短信内容
        try {
            resultMap = getResultMap(bo.getParameterJson(), sysMsgTemplateVo.getParameterJson());
        } catch (JsonProcessingException e) {
            log.error("短信发送失败：数据转换异常{}", e.getMessage());
            throw new ServiceException("短信发送失败，请检查参数是否正确");
        }

        // 发送
        SmsBlend smsBlend = SmsFactory.getSmsBlend(bo.getAccountId().toString());
        SmsResponse smsResponse = smsBlend.massTexting(phones, templateId, resultMap);

        // 插入消息监控数据
        insertMonitoring(bo.getAccountId(), bo.getMsgSource(), bo.getMsgType(), resultMap.toString(), null, users.toString(), loginUser.getUserId(), loginUser.getTenantId(), loginUser.getDeptId());

        // 响应值
        return analysis(smsResponse);


    }

    /**
     * 解析短信云响应值
     *
     * @param smsResponse 响应值
     */
    private SysMsgSendStatusVo analysis(SmsResponse smsResponse) {

        try {
            if (!smsResponse.isSuccess()) {
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> responseMap = objectMapper.readValue(smsResponse.getData().toString(), Map.class);

                // 获取腾讯云和阿里云的响应
                Object tencent = responseMap.get("Response");
                Object alibaba = responseMap.get("Message");

                if (ObjUtil.isNotNull(tencent)) {
                    return handleTencentError(tencent);
                } else if (ObjUtil.isNotNull(alibaba)) {
                    return handleAlibabaError(alibaba);
                }
            }
        } catch (JsonProcessingException e) {
            log.error("短信响应值解析失败,{}", e.getMessage());
        }

        return msgSendResponse(SysMsgTypeEnum.SMS.getMsgType(), StatusEnum.YES.getCode(), null);
    }


    /**
     * 解析腾讯云响应值
     *
     * @param tencent 腾讯云
     */
    private SysMsgSendStatusVo handleTencentError(Object tencent) {

        Map<String, Object> tencentMap = (Map<String, Object>) tencent;
        Map<String, Object> errorMap = (Map<String, Object>) tencentMap.get("Error");
        String message = (String) errorMap.get("Message");

        return msgSendResponse(SysMsgTypeEnum.SMS.getMsgType(), StatusEnum.NO.getCode(), message);

    }

    /**
     * 解析阿里云响应值
     *
     * @param alibaba 腾讯云
     */
    private SysMsgSendStatusVo handleAlibabaError(Object alibaba) {
        String message = alibaba.toString();
        return msgSendResponse(SysMsgTypeEnum.SMS.getMsgType(), StatusEnum.NO.getCode(), message);

    }


    /**
     * 根据状态返回指定参数
     *
     * @param msgType 消息类型【0：站内信；1：邮件；2：短信；3：钉钉；4：企业微信；5：webhook；6：微信公众号】
     * @param status  是否发送成功
     * @param message 错误内容
     */
    private SysMsgSendStatusVo msgSendResponse(Integer msgType, String status, String message) {
        SysMsgSendStatusVo vo = new SysMsgSendStatusVo();
        vo.setMsgType(msgType);
        vo.setStatus(status);
        vo.setCause(message);
        return vo;
    }


    /**
     * 发送邮件
     *
     * @param bo            发送配置（根据 id 获取对应人与模板）
     * @param userVoMap     所有发送配置的接收人 <key：用户id, value：用户数据>
     * @param templateVoMap 所有发送配置的消息模板 <key：消息模板id, value：模板数据>
     * @param loginUser     当前登陆人（消息发送者）
     */
    private SysMsgSendStatusVo sendMail(SysMsgSendBo bo, Map<Long, SysUserVo> userVoMap, Map<Long, SysMsgTemplateVo> templateVoMap, LoginUser loginUser) {

        // 消息监控数据 - 接收人
        List<String> users = new ArrayList<>();

        // 获取邮箱账号
        MailAccount account = getMailAccount(bo);

        // 当前发送配置需要发送的邮箱
        StringBuilder emails = new StringBuilder();
        bo.getUserIds().forEach(user -> {
            SysUserVo sysUserVo = userVoMap.get(user);
            emails.append(sysUserVo.getEmail()).append(",");
            users.add(sysUserVo.getNickName());
        });

        // 当前发送配置需要发送的内容
        SysMsgTemplateVo sysMsgTemplateVo = templateVoMap.get(Long.parseLong(bo.getId()));
        String title;
        String content;
        try {
            title = getResultString(bo.getMsgTitle(), sysMsgTemplateVo.getMsgTitle());
            content = getResultString(bo.getMsgContent(), sysMsgTemplateVo.getMsgContent());
        } catch (JsonProcessingException e) {
            log.error("邮件发送失败：数据转换异常{}", e.getMessage());
            throw new ServiceException("邮件发送失败，请检查参数是否正确");
        }

        // 发送
        String result = MailUtils.send(account, true, MailUtils.splitAddress(emails.toString()), null, null, title, content, null, false, (File[]) null);

        // 插入消息监控数据
        insertMonitoring(bo.getAccountId(), bo.getMsgSource(), bo.getMsgType(), title, content, users.toString(), loginUser.getUserId(), loginUser.getTenantId(), loginUser.getDeptId());

        // 响应值
        return msgSendResponse(SysMsgTypeEnum.MAIL.getMsgType(), StatusEnum.YES.getCode(), result);

    }


    /**
     * 获取邮箱账号
     */
    private MailAccount getMailAccount(SysMsgSendBo bo) {

        SysMsgMailConfigVo sysMsgMailConfigVo = new SysMsgMailConfigVo();

        Object cacheMapValue = RedisUtils.getCacheMapValue(CacheNames.MAIL, bo.getAccountId().toString());
        if (ObjUtil.isNotNull(cacheMapValue)) {
            if (cacheMapValue instanceof SysMsgMailConfigVo) {
                sysMsgMailConfigVo = (SysMsgMailConfigVo) cacheMapValue;
            }
        } else {
            sysMsgMailConfigVo = sysMsgMailConfigMapper.selectVoById(bo.getAccountId());
        }

        MailAccount account = new MailAccount();
        account.setHost(sysMsgMailConfigVo.getHost());
        account.setPort(Integer.parseInt(sysMsgMailConfigVo.getPort()));
        account.setAuth(StatusEnum.YES.is(sysMsgMailConfigVo.getAuth()));
        account.setFrom(sysMsgMailConfigVo.getSender());
        account.setUser(sysMsgMailConfigVo.getUser());
        account.setPass(sysMsgMailConfigVo.getPass());
        account.setSocketFactoryPort(Integer.parseInt(sysMsgMailConfigVo.getPort()));
        account.setStarttlsEnable(StatusEnum.YES.is(sysMsgMailConfigVo.getStarttls()));

        return account;
    }

    /**
     * 发送站内信
     *
     * @param bo            发送配置（根据 id 获取对应人与模板）
     * @param userVoMap     所有发送配置的接收人 <key：用户id, value：用户数据>
     * @param templateVoMap 所有发送配置的消息模板 <key：消息模板id, value：模板数据>
     * @param loginUser     当前登陆人（消息发送者）
     */
    private SysMsgSendStatusVo sendInternal(SysMsgSendBo bo, Map<Long, SysUserVo> userVoMap, Map<Long, SysMsgTemplateVo> templateVoMap, LoginUser loginUser) {

        // 消息监控数据 - 接收人
        List<String> users = new ArrayList<>();

        // 当前发送配置需要发送的用户 ids
        List<Long> userIds = bo.getUserIds();

        // 当前发送配置需要发送的内容
        SysMsgTemplateVo sysMsgTemplateVo = templateVoMap.get(Long.parseLong(bo.getId()));

        String title;
        String content;

        try {
            title = getResultString(bo.getMsgTitle(), sysMsgTemplateVo.getMsgTitle());
            content = getResultString(bo.getMsgContent(), sysMsgTemplateVo.getMsgContent());

        } catch (JsonProcessingException e) {
            log.error("站内信发送失败：数据转换异常{}", e.getMessage());
            throw new ServiceException("站内信发送失败，请检查参数是否正确");
        }

        // 发送
        SysMsgInternal sysMsgInternal = new SysMsgInternal();
        sysMsgInternal.setMsgSource(sysMsgTemplateVo.getMsgSource());
        sysMsgInternal.setMsgTitle(title);
        sysMsgInternal.setMsgContent(content);
        sysMsgInternalMapper.insert(sysMsgInternal);

        List<SysMsgInternalUser> sysMsgInternalUsers = new ArrayList<>();
        userIds.forEach(userId -> {

            // 消息发送 - 站内信数据
            SysMsgInternalUser sysMsgInternalUser = new SysMsgInternalUser();
            sysMsgInternalUser.setInternalId(sysMsgInternal.getId());
            sysMsgInternalUser.setUserId(userId);
            sysMsgInternalUser.setStatus(StatusEnum.NO.getCode());
            sysMsgInternalUsers.add(sysMsgInternalUser);

            // 消息监控 - 接收人数据
            SysUserVo sysUserVo = userVoMap.get(userId);
            users.add(sysUserVo.getNickName());

        });
        sysMsgInternalUserMapper.insert(sysMsgInternalUsers);

        SseMessageDto dto = new SseMessageDto();
        dto.setUserIds(userIds);
        dto.setMessage(title);
        SseMessageUtils.publishMessage(dto);

        // 插入消息监控数据
        insertMonitoring(bo.getAccountId(), bo.getMsgSource(), bo.getMsgType(), title, content, users.toString(), loginUser.getUserId(), loginUser.getTenantId(), loginUser.getDeptId());

        return msgSendResponse(SysMsgTypeEnum.INTERNAL.getMsgType(), StatusEnum.YES.getCode(), null);

    }

    /**
     * 异步 - 插入消息监控数据
     * 多线程下 bo 数据安全性，不传递 bo 对象。不能获取发送人数据
     *
     * @param accountId  账号 id
     * @param msgSource  消息来源【0：公告；1：流程；2：系统；3：日程】
     * @param msgType    消息类型【0：站内信；1：邮件；2：短信；3：钉钉；4：企业微信；5：webhook；6：微信公众号】
     * @param msgTitle   消息标题
     * @param msgContent 消息内容
     * @param realNames  接收人
     * @param createBy   发送人
     * @param tenantId   租户编号
     * @param createDept 创建部门
     */
    private void insertMonitoring(Long accountId, Integer msgSource, Integer msgType, String msgTitle, String msgContent, String realNames, Long createBy, String tenantId, Long createDept) {

        ThreadUtil.execAsync(() -> {

            SysMsgMonitoring sysMsgMonitoring = new SysMsgMonitoring();
            sysMsgMonitoring.setMsgSource(msgSource);
            sysMsgMonitoring.setMsgType(msgType);
            sysMsgMonitoring.setMsgTitle(msgTitle);
            sysMsgMonitoring.setMsgContent(msgContent);
            sysMsgMonitoring.setAccountId(accountId);
            sysMsgMonitoring.setRealNames(realNames);
            sysMsgMonitoring.setCreateBy(createBy);
            sysMsgMonitoring.setTenantId(tenantId);
            sysMsgMonitoring.setCreateDept(createDept);

            sysMsgMonitoringMapper.insert(sysMsgMonitoring);
        });

    }

    /**
     * 校验编码唯一性
     * 新增、修改配置时，校验编码是否重复
     *
     * @param code 编码
     * @param id   id
     */
    private void verifyCodeUnique(String code, Long id) {

        boolean exists = sysMsgMailConfigMapper.exists(
            new LambdaQueryWrapper<SysMsgMailConfig>()
                .eq(SysMsgMailConfig::getCode, code)
                .ne(ObjectUtil.isNotNull(id), SysMsgMailConfig::getId, id)
        );

        if (exists) {
            throw new ServiceException("编码重复");
        }
    }

    /**
     * 短信发送-短信动态参数数据转换
     * 示例：
     * 接参数据 Json： {"@name": "测试", "@num1": "100", "@num2": "1", "@time": "2024-10-10"}
     * 模板数据 Json： {"name": "@name", "num1": "@num1", "num2": "@num2", "time": "@time"}
     * 转换为 LinkedHashMap<String,String>：{"name": "测试", "num1": "100", "num2": "1", "time": "2024-10-10"}
     * key 对应短信云模板里的变量
     *
     * @param userParameterJson 接参
     * @param dbParameterJson   模板
     */
    private LinkedHashMap<String, String> getResultMap(String userParameterJson, String dbParameterJson) throws JsonProcessingException {

        ObjectMapper objectMapper = new ObjectMapper();
        LinkedHashMap<String, String> resultMap = new LinkedHashMap<>();

        Map<String, String> userMap = objectMapper.readValue(userParameterJson, Map.class);

        Map<String, String> dbMap = objectMapper.readValue(dbParameterJson, Map.class);

        dbMap.forEach((key, value) -> {
            resultMap.put(key, userMap.getOrDefault(value, value));
        });

        return resultMap;
    }

    /**
     * 消息发送-标题内容数据转换
     * 示例：
     * 接参数据 Json： {"$time": "1970-01-01", "$num1": "1", "$num2": "99", "$name": "张三"}
     * 模板数据 String： 狂欢盛典，${time}新品开抢，前${num1}分钟再享${num2}折！今晚${name}直播预热
     * 转换为 String：狂欢盛典，1970-01-01新品开抢，前1分钟再享99折！今晚张三直播预热
     *
     * @param userParameterJson 接参
     * @param dbParameter       模板
     */
    private String getResultString(String userParameterJson, String dbParameter) throws JsonProcessingException {

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> userParameterMap = objectMapper.readValue(userParameterJson, Map.class);

        for (Map.Entry<String, String> entry : userParameterMap.entrySet()) {
            // 只替换大括号中的数据
            dbParameter = dbParameter.replaceAll("\\{" + entry.getKey() + "\\}", entry.getValue());
        }

        return dbParameter;
    }

}
