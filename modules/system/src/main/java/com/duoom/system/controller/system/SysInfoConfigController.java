package com.duoom.system.controller.system;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.duoom.common.core.domain.R;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.web.core.BaseController;
import com.duoom.system.domain.bo.SysInfoConfigBo;
import com.duoom.system.domain.vo.SysInfoConfigVo;
import com.duoom.system.service.ISysInfoConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 *  系统信息配置
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/info/config")
public class SysInfoConfigController extends BaseController {

    private final ISysInfoConfigService iSysInfoConfigService;

    /**
     * 系统信息配置-查询
     */
    @SaIgnore
    @GetMapping
    public R<SysInfoConfigVo> query() {
        return R.ok(iSysInfoConfigService.query());
    }

    /**
     * 系统信息配置-更新
     */
    @SaCheckPermission("system:sendConfig:edit")
    @Log(title = "系统信息配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/update")
    public R<Void> edit(@RequestBody SysInfoConfigBo bo) {
        return toAjax(iSysInfoConfigService.update(bo));
    }


}
