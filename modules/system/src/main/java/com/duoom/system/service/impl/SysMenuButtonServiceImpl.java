package com.duoom.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.duoom.common.core.domain.dto.MenuButtonDto;
import com.duoom.common.core.service.MenuButtonService;
import com.duoom.common.core.utils.MapstructUtils;
import com.duoom.common.core.utils.StringUtils;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.system.domain.SysMenuButton;
import com.duoom.system.domain.bo.SysMenuButtonBo;
import com.duoom.system.domain.vo.SysMenuButtonVo;
import com.duoom.system.mapper.SysMenuButtonMapper;
import com.duoom.system.service.ISysMenuButtonService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 菜单按钮Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RequiredArgsConstructor
@Service
public class SysMenuButtonServiceImpl implements ISysMenuButtonService, MenuButtonService {

    private final SysMenuButtonMapper baseMapper;

    /**
     * 查询菜单按钮
     *
     * @param id 主键
     * @return 菜单按钮
     */
    @Override
    public SysMenuButtonVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询菜单按钮列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 菜单按钮分页列表
     */
    @Override
    public TableDataInfo<SysMenuButtonVo> queryPageList(SysMenuButtonBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysMenuButton> lqw = buildQueryWrapper(bo);
        Page<SysMenuButtonVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的菜单按钮列表
     *
     * @param bo 查询条件
     * @return 菜单按钮列表
     */
    @Override
    public List<SysMenuButtonVo> queryList(SysMenuButtonBo bo) {
        LambdaQueryWrapper<SysMenuButton> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysMenuButton> buildQueryWrapper(SysMenuButtonBo bo) {
        LambdaQueryWrapper<SysMenuButton> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysMenuButton::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), SysMenuButton::getName, bo.getName());
        lqw.eq(bo.getMenuId() != null, SysMenuButton::getMenuId, bo.getMenuId());
        lqw.eq(StringUtils.isNotBlank(bo.getIcon()), SysMenuButton::getIcon, bo.getIcon());
        lqw.eq(StringUtils.isNotBlank(bo.getPerms()), SysMenuButton::getPerms, bo.getPerms());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), SysMenuButton::getUrl, bo.getUrl());
        lqw.eq(bo.getMethod() != null, SysMenuButton::getMethod, bo.getMethod());
        return lqw;
    }

    /**
     * 新增菜单按钮
     *
     * @param bo 菜单按钮
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysMenuButtonBo bo) {
        SysMenuButton add = MapstructUtils.convert(bo, SysMenuButton.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改菜单按钮
     *
     * @param bo 菜单按钮
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysMenuButtonBo bo) {
        SysMenuButton update = MapstructUtils.convert(bo, SysMenuButton.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysMenuButton entity) {
    }

    /**
     * 校验并批量删除菜单按钮信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


//    @Override
//    public List<MenuButtonDto> queryListByMenuId(Long menuId) {
//        return List.of();
//    }
//
//    @Override
//    public boolean deleteByMenuId(Long menuId) {
//        return false;
//    }
//
//    @Override
//    public boolean saveBatch(List<MenuButtonDto> btnList) {
//        return false;
//    }

    /**
     * 新增菜单按钮
     */
    @Override
    public void saveBatch(List<MenuButtonDto> btnList) {
        baseMapper.insertBatch(BeanUtil.copyToList(btnList, SysMenuButton.class));
    }

    /**
     * 删除菜单按钮
     */
    @Override
    public void remove(Long menuId) {
        baseMapper.delete(new LambdaQueryWrapper<SysMenuButton>().eq(SysMenuButton::getMenuId,menuId));
    }
}
