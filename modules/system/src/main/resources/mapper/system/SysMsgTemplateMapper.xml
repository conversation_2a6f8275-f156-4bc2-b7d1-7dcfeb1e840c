<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.duoom.system.mapper.SysMsgTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.duoom.system.domain.SysMsgTemplate">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="msg_source" property="msgSource" />
        <result column="msg_type" property="msgType" />
        <result column="msg_title" property="msgTitle" />
        <result column="msg_content" property="msgContent" />
        <result column="template_id" property="templateId" />
        <result column="parameter_json" property="parameterJson" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_dept" property="createDept" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

</mapper>
