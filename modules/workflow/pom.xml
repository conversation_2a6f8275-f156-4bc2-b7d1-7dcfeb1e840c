<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.duoom</groupId>
        <artifactId>modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>workflow</artifactId>

    <name>workflow</name>
    <description>
        workflow 工作流程
    </description>
    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-sse</artifactId>
        </dependency>

        <dependency>
            <groupId>com.duoom</groupId>
            <artifactId>common-magicapi</artifactId>
        </dependency>

        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter-webapp</artifactId>
            <exclusions>
                <!-- 排除FastJSON以避免与Jakarta EE 9+的兼容性问题 -->
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 解决 Camunda webapp 在 Spring Boot 3.x 中的兼容性问题 -->
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1.1</version>
        </dependency>

    </dependencies>
</project>
