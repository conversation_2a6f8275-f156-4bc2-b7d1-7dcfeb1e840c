<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_test"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  
  <bpmn:process id="test_import_process" name="测试导入流程" isExecutable="true">
    
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_start_to_task1</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="userTask1" name="一级审批">
      <bpmn:incoming>Flow_start_to_task1</bpmn:incoming>
      <bpmn:outgoing>Flow_task1_to_task2</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="userTask2" name="二级审批">
      <bpmn:incoming>Flow_task1_to_task2</bpmn:incoming>
      <bpmn:outgoing>Flow_task2_to_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_task2_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:sequenceFlow id="Flow_start_to_task1" sourceRef="StartEvent_1" targetRef="userTask1"/>
    <bpmn:sequenceFlow id="Flow_task1_to_task2" sourceRef="userTask1" targetRef="userTask2"/>
    <bpmn:sequenceFlow id="Flow_task2_to_end" sourceRef="userTask2" targetRef="EndEvent_1"/>
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="test_import_process">
      
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="150" y="100" width="36" height="36"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="UserTask1_di" bpmnElement="userTask1">
        <dc:Bounds x="250" y="80" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="UserTask2_di" bpmnElement="userTask2">
        <dc:Bounds x="400" y="80" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="550" y="100" width="36" height="36"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="Flow_start_to_task1_di" bpmnElement="Flow_start_to_task1">
        <di:waypoint x="186" y="118"/>
        <di:waypoint x="250" y="118"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="Flow_task1_to_task2_di" bpmnElement="Flow_task1_to_task2">
        <di:waypoint x="350" y="118"/>
        <di:waypoint x="400" y="118"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="Flow_task2_to_end_di" bpmnElement="Flow_task2_to_end">
        <di:waypoint x="500" y="118"/>
        <di:waypoint x="550" y="118"/>
      </bpmndi:BPMNEdge>
      
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>