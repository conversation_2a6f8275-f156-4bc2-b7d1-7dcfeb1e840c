package com.duoom.workflow.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 批量审批任务DTO
 *
 * <AUTHOR>
 * @date 2025/01/23
 */
@Data
public class BatchApproveDto implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID列表
     */
    @NotEmpty(message = "任务ID列表不能为空")
    private List<String> taskIds;
    
    /**
     * 审批动作（只支持同意或拒绝）
     */
    @NotNull(message = "审批动作不能为空")
    private ApproveDto.ApproveAction action;
    
    /**
     * 审批意见
     */
    @NotBlank(message = "审批意见不能为空")
    private String comment;
    
    /**
     * 流程变量（可选）
     */
    private Map<String, Object> variables;
}