package com.duoom.workflow.domain.bo;

import com.duoom.workflow.domain.FlowSchemaHistory;
import com.duoom.common.mybatis.core.domain.BaseEntity;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 流程模板历史记录业务对象 flow_schema_history
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FlowSchemaHistory.class, reverseConvertGenerate = false)
public class FlowSchemaHistoryBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 工作流模板id
     */
    private Long schemaId;

    /**
     * 是否为活动版本
     */
    private String activityFlag;

    /**
     * xml内容
     */
    private String xmlContent;

    /**
     * json内容
     */
    private String jsonContent;

    /**
     * 流程定义Id
     */
    private String definitionId;

    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 部署ID
     */
    private String deploymentId;

    /**
     * 流程图地址
     */
    private String workflowChat;

    /**
     * 是否删除
     */
    @NotNull(message = "是否删除不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer deleteMark;

    /**
     * 状态（Y正常 N无效）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
