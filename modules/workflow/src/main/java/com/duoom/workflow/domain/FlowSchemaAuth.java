package com.duoom.workflow.domain;

import com.duoom.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 流程模板权限对象 flow_schema_auth
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flow_schema_auth")
public class FlowSchemaAuth extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模板信息id
     */
    private Long schemaId;

    /**
     * 对应主键Id
     */
    private Long objId;

    /**
     * 对象名称
     */
    private String objName;

    /**
     * 对应对象类型 1岗位 2角色 3用户 4所用人可看
     */
    private Integer objType;


}
