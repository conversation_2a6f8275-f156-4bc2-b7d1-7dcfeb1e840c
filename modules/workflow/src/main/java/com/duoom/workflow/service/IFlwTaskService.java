package com.duoom.workflow.service;

import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.workflow.domain.bo.TodoTaskQueryBo;
import com.duoom.workflow.domain.vo.TodoTaskVo;
import com.duoom.workflow.domain.dto.ApproveDto;
import com.duoom.workflow.domain.dto.BatchApproveDto;
import com.duoom.workflow.domain.dto.TaskUrgeDto;

import java.util.List;

/**
 * 工作流任务服务接口
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
public interface IFlwTaskService {

    /**
     * 分页查询待办任务
     *
     * @param queryBo 查询条件
     * @return 待办任务分页数据
     */
    TableDataInfo<TodoTaskVo> queryTodoTaskPage(TodoTaskQueryBo queryBo);

    /**
     * 查询待办任务列表
     *
     * @param queryBo 查询条件
     * @return 待办任务列表
     */
    List<TodoTaskVo> queryTodoTaskList(TodoTaskQueryBo queryBo);

    /**
     * 查询待办任务数量
     *
     * @param userId 用户ID
     * @return 待办任务数量
     */
    Long countTodoTask(String userId);

    /**
     * 获取待办任务详情
     *
     * @param taskId 任务ID
     * @return 待办任务详情
     */
    TodoTaskVo getTodoTaskDetail(String taskId);

    /**
     * 查询用户的待办任务（包含代理和委托）
     *
     * @param userId 用户ID
     * @return 待办任务列表
     */
    List<TodoTaskVo> queryUserTodoTasks(String userId);
    
    /**
     * 审批任务
     *
     * @param dto 审批信息
     */
    void approveTask(ApproveDto dto);
    
    /**
     * 批量审批任务
     *
     * @param dto 批量审批信息
     */
    void batchApprove(BatchApproveDto dto);
    
    /**
     * 认领任务
     *
     * @param taskId 任务ID
     */
    void claimTask(String taskId);
    
    /**
     * 取消认领任务
     *
     * @param taskId 任务ID
     */
    void unclaimTask(String taskId);
    
    /**
     * 获取审批记录
     *
     * @param processInstanceId 流程实例ID
     * @return 审批记录列表
     */
    List<Object> getApproveHistory(String processInstanceId);
    
    /**
     * 催办任务
     *
     * @param dto 催办信息
     */
    void urgeTask(TaskUrgeDto dto);
}