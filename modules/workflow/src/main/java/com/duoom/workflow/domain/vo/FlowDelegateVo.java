package com.duoom.workflow.domain.vo;

import com.duoom.common.translation.annotation.Translation;
import com.duoom.common.translation.constant.TransConstant;
import com.duoom.workflow.domain.FlowDelegate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 流程委托视图对象 flow_delegate
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@AutoMapper(target = FlowDelegate.class)
public class FlowDelegateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 委托人
     */
    private Long userId;

    /**
     * 委托人姓名
     */
    @Translation(type = TransConstant.USER_ID_TO_REAL_NAME, mapper = "userId")
    private String delegator;

    /**
     * 被委托人ids
     */
    private String delegateUserIds;

    /**
     * 被委托人姓名
     */
    private String delegateUserNames;

    /**
     * 委托模板ids
     */
    private String schemaIds;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 备注
     */
    private String remark;


}
