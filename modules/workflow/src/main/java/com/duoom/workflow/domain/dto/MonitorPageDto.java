package com.duoom.workflow.domain.dto;

import com.duoom.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 流程监控分页查询对象
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorPageDto extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String keyword;

    private Integer processStatus;

    private String schemaId;

    private String startUserId;

    private String assignee;

    private String startTimeBegin;

    private String startTimeEnd;

    private Integer urgency;

    private Boolean onlyMine;

    private Boolean onlyTodo;
}