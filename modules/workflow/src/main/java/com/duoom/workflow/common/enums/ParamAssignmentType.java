package com.duoom.workflow.common.enums;

/**
 * @author: <PERSON><PERSON>
 * @Date: 2022/10/17 14:59
 */
public enum ParamAssignmentType {
    /**
     * 值
     */
    VALUE(0, "值"),
    /**
     * 变量
     */
    VAR(1, "流程变量"),
    /**
     * api
     */
    API(2, "API"),
    /**
     * 变量
     */
    FORM(3, "表单");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    ParamAssignmentType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
