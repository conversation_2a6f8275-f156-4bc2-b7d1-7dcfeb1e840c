package com.duoom.workflow.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.duoom.common.json.utils.JsonUtils;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.core.utils.StreamUtils;
import com.duoom.common.core.utils.StringUtils;
import com.duoom.common.redis.utils.RedisUtils;
import com.duoom.common.satoken.utils.LoginHelper;
import com.duoom.workflow.common.constant.FlowConstant;
import com.duoom.workflow.common.enums.FlowMemberType;
import com.duoom.workflow.domain.FlowApproveRecord;
import com.duoom.workflow.domain.FlowExtra;
import com.duoom.workflow.domain.FlowRecord;
import com.duoom.workflow.domain.FlowSchema;
import com.duoom.workflow.domain.FlowTask;
import com.duoom.workflow.domain.bo.LaunchProcessDto;
import com.duoom.workflow.domain.bo.RelaunchProcessDto;
import com.duoom.workflow.domain.config.*;
import com.duoom.workflow.domain.vo.FlowRecordVo;
import com.duoom.workflow.domain.vo.FlowSchemaVo;
import com.duoom.workflow.domain.vo.LaunchProcessVo;
import com.duoom.workflow.mapper.*;
import com.duoom.workflow.service.IFlwProcessService;
import com.duoom.workflow.service.IFlwSchemaService;
import com.duoom.workflow.utils.WorkFlowUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 流程处理
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlwProcessServiceImpl implements IFlwProcessService {

    private final FlowSchemaMapper flowSchemaMapper;
    private final FlowRecordMapper flowRecordMapper;
    private final FlowExtraMapper flowExtraMapper;
    private final FlowApproveRecordMapper flowApproveRecordMapper;
    private final FlowDraftMapper flowDraftMapper;
    private final FlowTaskMapper flowTaskMapper;

    private final HistoryService historyService;
    private final RuntimeService runtimeService;
    private final TaskService taskService;

    private final IFlwSchemaService schemaService;


    /**
     * 查询流程记录
     *
     * @param processInstanceId 流程实例id
     * @return 流程记录
     */
    @Override
    public List<FlowRecordVo> queryListByProcessInstanceId(String processInstanceId) {
        List<HistoricProcessInstance> subProcess = historyService.createHistoricProcessInstanceQuery()
            .superProcessInstanceId(processInstanceId).list();

        List<String> processIds = StreamUtils.toList(subProcess, HistoricProcessInstance::getId);
        processIds.add(processInstanceId);

        LambdaQueryWrapper<FlowRecord> lqw  = new LambdaQueryWrapper<FlowRecord>();
        lqw.in(FlowRecord::getProcessId, processIds);
        lqw.orderByDesc(FlowRecord::getId);
        return flowRecordMapper.selectVoList(lqw);
    }

    /**
     * 发起流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LaunchProcessVo launchProcess(LaunchProcessDto dto) {
        try {
            // 1. 验证流程模版
            FlowSchemaVo schema = validateAndGetSchema(dto.getSchemaId());

            // 2. 验证表单数据
            validateFormData(schema, dto.getFormData());

            // 3. 准备流程变量
            Map<String, Object> variables = prepareProcessVariables(schema, dto);

            // 4. 生成流程编号
            String serialNumber = generateSerialNumber(schema);
            variables.put("serialNumber", serialNumber);

            // 5. 生成流程标题
            String processTitle = generateProcessTitle(schema, dto);
            variables.put("processTitle", processTitle);

            // 6. 启动流程实例
            ProcessInstance instance = runtimeService.startProcessInstanceByKey(
                schema.getDefinitionKey(),
                dto.getBusinessKey(),
                variables
            );

            // 7. 创建流程扩展信息
            createFlowExtra(instance, schema, dto, serialNumber, processTitle);

            // 8. 记录流程启动日志
            recordProcessStart(instance, schema);

            // 9. 处理第一个任务
            TaskInfo firstTask = handleFirstTask(instance.getId());

            // 10. 同步任务到flow_task表
            syncTasksToFlowTask(instance.getId(), schema.getId());

            // 11. 发送流程启动通知
            sendProcessStartNotification(instance, firstTask);

            // 12. 触发流程启动事件
            publishProcessStartEvent(instance, schema);

            return buildLaunchResponse(instance, schema, serialNumber, processTitle, firstTask);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("流程发起失败", e);
            throw new ServiceException("流程发起失败：" + e.getMessage());
        }
    }

    /**
     * 预览流程
     */
    @Override
    public LaunchProcessVo previewProcess(LaunchProcessDto dto) {
        // 验证流程模版
        FlowSchemaVo schema = validateAndGetSchema(dto.getSchemaId());

        // 准备预览数据
        String serialNumber = generateSerialNumber(schema);
        String processTitle = generateProcessTitle(schema, dto);

        LaunchProcessVo vo = new LaunchProcessVo();
        vo.setProcessInstanceId("PREVIEW-" + UUID.randomUUID());
        vo.setSerialNumber(serialNumber);
        vo.setProcessTitle(processTitle);
        vo.setSchemaId(schema.getId());
        vo.setSchemaCode(schema.getCode());
        vo.setSchemaName(schema.getName());
        vo.setStartUserId(LoginHelper.getUserId());
        vo.setStartUserName(LoginHelper.getUsername());
        vo.setLaunchTime(LocalDateTime.now());
        vo.setStatus(0);

        return vo;
    }

    /**
     * 保存草稿
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDraft(LaunchProcessDto dto) {
        // TODO: 实现草稿保存逻辑
        throw new ServiceException("草稿功能暂未实现");
    }

    /**
     * 重新发起流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LaunchProcessVo relaunchProcess(RelaunchProcessDto dto) {
        try {
            // 1. 验证原流程状态
            FlowExtra originalExtra = validateOriginalProcess(dto.getOriginalProcessInstanceId());

            // 2. 获取原流程的模版信息
            FlowSchemaVo schema = validateAndGetSchema(originalExtra.getSchemaId());

            // 3. 准备新流程的数据
            LaunchProcessDto launchDto = prepareLaunchDto(dto, originalExtra);

            // 4. 发起新流程
            LaunchProcessVo result = launchProcess(launchDto);

            // 5. 记录重新发起关联关系
            if (dto.getLinkOriginalProcess()) {
                recordRelaunchRelation(dto.getOriginalProcessInstanceId(), result.getProcessInstanceId(), dto.getRelaunchReason());
            }

            return result;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("重新发起流程失败", e);
            throw new ServiceException("重新发起流程失败：" + e.getMessage());
        }
    }

    /**
     * 验证原流程状态
     */
    private FlowExtra validateOriginalProcess(String processInstanceId) {
        // 查询原流程信息
        LambdaQueryWrapper<FlowExtra> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowExtra::getProcessId, processInstanceId);
        FlowExtra flowExtra = flowExtraMapper.selectOne(queryWrapper);

        if (flowExtra == null) {
            throw new ServiceException("原流程不存在");
        }

        // 查询流程实例状态
        HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(processInstanceId)
            .singleResult();

        if (historicInstance == null) {
            throw new ServiceException("原流程实例不存在");
        }

        // 只有已结束的流程才能重新发起
        if (historicInstance.getEndTime() == null) {
            throw new ServiceException("流程还在进行中，不能重新发起");
        }

        // 检查流程结束原因
        String deleteReason = historicInstance.getDeleteReason();
        if (deleteReason == null ||
            (!deleteReason.contains("退回") &&
             !deleteReason.contains("终止") &&
             !deleteReason.contains("作废") &&
             !deleteReason.contains("撤销"))) {
            // 如果是正常完成的流程，也允许重新发起（复制流程的场景）
            log.info("流程正常完成，允许重新发起作为复制流程");
        }

        return flowExtra;
    }

    /**
     * 准备发起流程的数据
     */
    private LaunchProcessDto prepareLaunchDto(RelaunchProcessDto dto, FlowExtra originalExtra) {
        LaunchProcessDto launchDto = new LaunchProcessDto();

        // 复制基础信息
        launchDto.setSchemaId(dto.getSchemaId() != null ? dto.getSchemaId() : originalExtra.getSchemaId());
        launchDto.setBusinessKey(dto.getBusinessKey());
        launchDto.setProcessTitle(dto.getProcessTitle());
        launchDto.setUrgency(dto.getUrgency());
        launchDto.setRemark(dto.getRemark());

        // 处理表单数据
        if (dto.getCopyFormData() && StringUtils.isNotBlank(originalExtra.getFormDataSnapshot())) {
            // 复制原表单数据
            Map<String, Object> originalFormData = JsonUtils.parseObject(originalExtra.getFormDataSnapshot(), Map.class);
            if (originalFormData != null) {
                // 如果新表单数据不为空，则合并数据（新数据优先）
                if (dto.getFormData() != null) {
                    originalFormData.putAll(dto.getFormData());
                }
                launchDto.setFormData(originalFormData);
            } else {
                launchDto.setFormData(dto.getFormData());
            }
        } else {
            launchDto.setFormData(dto.getFormData());
        }

        // 处理附件
        if (dto.getCopyAttachments() && dto.getAttachments() == null) {
            // TODO: 从原流程复制附件
            launchDto.setAttachments(dto.getAttachments());
        } else {
            launchDto.setAttachments(dto.getAttachments());
        }

        // 处理扩展参数
        Map<String, Object> extParams = dto.getExtParams() != null ? new HashMap<>(dto.getExtParams()) : new HashMap<>();
        extParams.put("isRelaunch", true);
        extParams.put("originalProcessInstanceId", dto.getOriginalProcessInstanceId());
        if (StringUtils.isNotBlank(dto.getRelaunchReason())) {
            extParams.put("relaunchReason", dto.getRelaunchReason());
        }
        launchDto.setExtParams(extParams);

        return launchDto;
    }

    /**
     * 记录重新发起关联关系
     */
    private void recordRelaunchRelation(String originalProcessId, String newProcessId, String reason) {
        FlowRecord record = new FlowRecord();
        record.setNodeId("relaunch");
        record.setNodeType("system");
        record.setNodeName("重新发起");
        record.setProcessId(originalProcessId);
        record.setMessage("流程已重新发起，新流程ID: " + newProcessId);
        if (StringUtils.isNotBlank(reason)) {
            record.setCirculateMessage("重新发起原因: " + reason);
        }
        record.setRecordTime(new Date());
        record.setCreateBy(LoginHelper.getUserId());
        record.setCreateTime(new Date());

        flowRecordMapper.insert(record);

        // 在新流程中也记录关联关系
        FlowRecord newRecord = new FlowRecord();
        newRecord.setNodeId("relaunch");
        newRecord.setNodeType("system");
        newRecord.setNodeName("重新发起");
        newRecord.setProcessId(newProcessId);
        newRecord.setMessage("该流程是从流程ID: " + originalProcessId + " 重新发起的");
        if (StringUtils.isNotBlank(reason)) {
            newRecord.setCirculateMessage("重新发起原因: " + reason);
        }
        newRecord.setRecordTime(new Date());
        newRecord.setCreateBy(LoginHelper.getUserId());
        newRecord.setCreateTime(new Date());

        flowRecordMapper.insert(newRecord);
    }

    /**
     * 验证并获取流程模版
     */
    private FlowSchemaVo validateAndGetSchema(Long schemaId) {
        FlowSchemaVo schema = schemaService.queryById(schemaId);
        if (schema == null) {
            throw new ServiceException("流程模版不存在");
        }
        if (StringUtils.isBlank(schema.getDefinitionId())) {
            throw new ServiceException("流程模版未部署，请先部署流程");
        }
        return schema;
    }

    /**
     * 验证表单数据
     */
    private void validateFormData(FlowSchemaVo schema, Map<String, Object> formData) {
        // 获取起始节点配置
        StartNodeConfig startConfig = getStartNodeConfig(schema);
        if (startConfig == null || startConfig.getFormConfigs() == null) {
            return;
        }

        // 验证每个表单的必填字段
        for (FormConfig formConfig : startConfig.getFormConfigs()) {
            if (formConfig.getChildren() != null) {
                for (FormItemConfig item : formConfig.getChildren()) {
                    if (item.getRequired() && formData.get(item.getKey()) == null) {
                        throw new ServiceException("字段[" + item.getFieldName() + "]不能为空");
                    }
                }
            }
        }
    }

    /**
     * 准备流程变量
     */
    private Map<String, Object> prepareProcessVariables(FlowSchemaVo schema, LaunchProcessDto dto) {
        Map<String, Object> variables = new HashMap<>();

        // 添加表单数据
        variables.putAll(dto.getFormData());

        // 添加系统变量
        variables.put("startUserId", LoginHelper.getUserId());
        variables.put("startUserName", LoginHelper.getUsername());
        variables.put("startTime", LocalDateTime.now());
        variables.put("schemaId", schema.getId());
        variables.put("schemaCode", schema.getCode());

        // 添加扩展参数
        if (dto.getExtParams() != null) {
            variables.putAll(dto.getExtParams());
        }

        // 处理流程参数配置
        processSchemaParams(schema, variables);

        // 添加流程发起人变量（重要：必须设置initiator变量）
        variables.put("initiator", LoginHelper.getUsername());  // 使用用户名而不是ID

        // 添加审批人变量（临时处理，实际应该从组织架构中获取）
        // TODO: 从当前用户的组织架构中获取实际的审批人
        variables.put("directManager", "admin");  // 直属主管
        variables.put("deptManager", "admin");    // 部门经理
        variables.put("hrManager", "admin");      // 人事经理

        return variables;
    }

    /**
     * 生成流程编号
     */
    private String generateSerialNumber(FlowSchemaVo schema) {
        FlowSchemaConfig config = JsonUtils.parseObject(schema.getJsonContent(), FlowSchemaConfig.class);

        if (config == null || config.getProcessConfig() == null || config.getProcessConfig().getNameRuleConfigs() == null || config.getProcessConfig().getNameRuleConfigs().isEmpty()) {
            // 默认规则：前缀+日期+4位序号
            return "FLOW" + DateUtil.format(new Date(), "yyyyMMdd") + getNextSerial(schema.getCode(), 4);
        }

        ProcessConfig processConfig = config.getProcessConfig();

        // 使用配置的命名规则生成流程编号
        StringBuilder number = new StringBuilder();
        for (NameRuleConfig rule : processConfig.getNameRuleConfigs()) {
            switch (rule.getType()) {
                case "text":
                    number.append(rule.getValue());
                    break;
                case "date":
                    number.append(DateUtil.format(new Date(), rule.getFormat()));
                    break;
                case "serial":
                    String serial = getNextSerial(schema.getCode(), rule.getLength());
                    number.append(serial);
                    break;
            }
        }

        return number.toString();
    }

    /**
     * 获取下一个序列号
     */
    private String getNextSerial(String schemaCode, Integer length) {
        String key = FlowConstant.FLOW_SERIAL_KEY + schemaCode + ":" + DateUtil.format(new Date(), "yyyyMMdd");
        long nextVal = RedisUtils.incrAtomicValue(key);
        if (nextVal == 1) {
            // 设置过期时间为第二天凌晨
            RedisUtils.expire(key, Duration.ofDays(1));
        }
        return StringUtils.leftPad(String.valueOf(nextVal), length, "0");
    }

    /**
     * 生成流程标题
     */
    private String generateProcessTitle(FlowSchemaVo schema, LaunchProcessDto dto) {
        if (StringUtils.isNotBlank(dto.getProcessTitle())) {
            return dto.getProcessTitle();
        }

        // 默认标题：流程名称-发起人-日期
        return schema.getName() + "-" + LoginHelper.getUsername() + "-" + DateUtil.format(new Date(), "yyyyMMdd");
    }

    /**
     * 创建流程扩展信息
     */
    private void createFlowExtra(ProcessInstance instance, FlowSchemaVo schema, LaunchProcessDto dto,
                                 String serialNumber, String processTitle) {
        FlowExtra extra = new FlowExtra();
        extra.setProcessId(instance.getId());
        extra.setSchemaId(schema.getId());
        extra.setSerialNumber(serialNumber);
        extra.setProcessTitle(processTitle);
        extra.setStartUserId(LoginHelper.getUserId());
        extra.setStartUserName(LoginHelper.getUsername());
        extra.setStartTime(LocalDateTime.now());
        extra.setLaunchTime(LocalDateTime.now());
        extra.setCurrentProgress("已发起");
        extra.setUrgeStatus(0);
        extra.setUrgency(dto.getUrgency() != null ? dto.getUrgency() : 0);

        // 保存表单数据快照
        if (dto.getFormData() != null) {
            extra.setFormDataSnapshot(JsonUtils.toJsonString(dto.getFormData()));
        }

        flowExtraMapper.insert(extra);
    }

    /**
     * 记录流程启动日志
     */
    private void recordProcessStart(ProcessInstance instance, FlowSchemaVo schema) {
        FlowRecord record = new FlowRecord();
        record.setNodeId("startEvent");
        record.setNodeType("startEvent");
        record.setNodeName("开始");
        record.setProcessId(instance.getId());
        record.setSchemaId(schema.getId());
        record.setMessage(LoginHelper.getUsername() + " 发起了流程");
        record.setRecordTime(new Date());
        record.setCreateBy(LoginHelper.getUserId());
        record.setCreateTime(new Date());

        flowRecordMapper.insert(record);
    }

    /**
     * 处理第一个任务
     */
    private TaskInfo handleFirstTask(String processInstanceId) {
        List<Task> tasks = taskService.createTaskQuery()
            .processInstanceId(processInstanceId)
            .active()
            .list();

        if (tasks.isEmpty()) {
            return null;
        }

        Task firstTask = tasks.get(0);
        TaskInfo taskInfo = new TaskInfo();
        taskInfo.setTaskId(firstTask.getId());
        taskInfo.setTaskName(firstTask.getName());
        taskInfo.setAssignees(new ArrayList<>());

        // 获取任务候选人
        if (StringUtils.isNotBlank(firstTask.getAssignee())) {
            taskInfo.getAssignees().add(firstTask.getAssignee());
        }

        return taskInfo;
    }

    /**
     * 发送流程启动通知
     */
    private void sendProcessStartNotification(ProcessInstance instance, TaskInfo firstTask) {
        // TODO: 实现消息通知逻辑
        log.info("流程启动通知：processId={}, firstTask={}", instance.getId(), firstTask);
    }

    /**
     * 触发流程启动事件
     */
    private void publishProcessStartEvent(ProcessInstance instance, FlowSchemaVo schema) {
        // TODO: 实现事件发布逻辑
        log.info("流程启动事件：processId={}, schemaId={}", instance.getId(), schema.getId());
    }

    /**
     * 构建响应对象
     */
    private LaunchProcessVo buildLaunchResponse(ProcessInstance instance, FlowSchemaVo schema,
                                               String serialNumber, String processTitle, TaskInfo firstTask) {
        LaunchProcessVo vo = new LaunchProcessVo();
        vo.setProcessInstanceId(instance.getId());
        vo.setSerialNumber(serialNumber);
        vo.setProcessTitle(processTitle);
        vo.setLaunchTime(LocalDateTime.now());
        vo.setSchemaId(schema.getId());
        vo.setSchemaCode(schema.getCode());
        vo.setSchemaName(schema.getName());
        vo.setStartUserId(LoginHelper.getUserId());
        vo.setStartUserName(LoginHelper.getUsername());
        vo.setBusinessKey(instance.getBusinessKey());
        vo.setStatus(0);
        vo.setCurrentNodeInfo("已发起");

        if (firstTask != null) {
            vo.setFirstTaskId(firstTask.getTaskId());
            vo.setFirstTaskName(firstTask.getTaskName());
            vo.setFirstTaskAssignees(firstTask.getAssignees());
        }

        return vo;
    }

    /**
     * 获取起始节点配置
     */
    private StartNodeConfig getStartNodeConfig(FlowSchemaVo schema) {
        FlowSchemaConfig config = JsonUtils.parseObject(schema.getJsonContent(), FlowSchemaConfig.class);
        if (config == null || config.getChildNodeConfig() == null) {
            return null;
        }

        // 从childNodeConfig中查找开始节点配置
        for (Map<String, Object> nodeMap : config.getChildNodeConfig()) {
            String nodeType = (String) nodeMap.get("type");
            if ("startEvent".equals(nodeType) || "bpmn:StartEvent".equals(nodeType)) {
                // 将Map转换为StartNodeConfig
                return JsonUtils.parseObject(JsonUtils.toJsonString(nodeMap), StartNodeConfig.class);
            }
        }
        return null;
    }

    /**
     * 处理流程参数配置
     */
    private void processSchemaParams(FlowSchemaVo schema, Map<String, Object> variables) {
        FlowSchemaConfig config = JsonUtils.parseObject(schema.getJsonContent(), FlowSchemaConfig.class);
        if (config == null || config.getProcessConfig() == null || config.getProcessConfig().getProcessParamConfigs() == null) {
            return;
        }

        // Call WorkFlowUtil.getProcessParam with correct parameters
        Map<String, Object> processParams = WorkFlowUtil.getProcessParam(
            config.getProcessConfig().getProcessParamConfigs(),
            schema,
            null, // serialNumber will be generated later if needed
            variables,
            LoginHelper.getLoginUser()
        );

        // Add all process parameters to variables
        if (processParams != null) {
            variables.putAll(processParams);
        }
    }

    /**
     * 任务信息内部类
     */
    @Data
    private static class TaskInfo {
        private String taskId;
        private String taskName;
        private List<String> assignees;
    }

    /**
     * 同步Camunda任务到flow_task表
     */
    private void syncTasksToFlowTask(String processInstanceId, Long schemaId) {
        // 查询流程实例的所有活动任务
        List<Task> tasks = taskService.createTaskQuery()
            .processInstanceId(processInstanceId)
            .active()
            .list();

        for (Task task : tasks) {
            // 检查是否已存在
            LambdaQueryWrapper<FlowTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlowTask::getTaskId, task.getId());
            if (flowTaskMapper.selectCount(queryWrapper) > 0) {
                continue;
            }

            // 创建flow_task记录
            FlowTask flowTask = new FlowTask();
            flowTask.setSchemaId(schemaId);
            flowTask.setProcessInstanceId(processInstanceId);
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefinitionKey(task.getTaskDefinitionKey());
            flowTask.setTaskName(task.getName());
            flowTask.setTaskStatus(0); // 待处理
            flowTask.setPriority(task.getPriority());
            flowTask.setDueDate(task.getDueDate());

            // 获取流程实例以获取businessKey
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
            if (processInstance != null) {
                flowTask.setBusinessKey(processInstance.getBusinessKey());
            }

            // 设置处理人或候选人
            if (StringUtils.isNotBlank(task.getAssignee())) {
                // 已分配的任务
                try {
                    flowTask.setAssignee(Long.parseLong(task.getAssignee()));
                    flowTask.setTaskStatus(1); // 已认领
                    flowTask.setClaimTime(new Date());
                } catch (NumberFormatException e) {
                    // assignee可能是用户名而不是ID，暂时不设置assignee
                    log.warn("任务处理人不是有效的用户ID: {}", task.getAssignee());
                }
            } else {
                // 获取候选用户和候选组
                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                List<String> candidateUsers = new ArrayList<>();
                List<String> candidateGroups = new ArrayList<>();

                for (IdentityLink link : identityLinks) {
                    if ("candidate".equals(link.getType())) {
                        if (link.getUserId() != null) {
                            candidateUsers.add(link.getUserId());
                        }
                        if (link.getGroupId() != null) {
                            candidateGroups.add(link.getGroupId());
                        }
                    }
                }

                if (!candidateUsers.isEmpty()) {
                    flowTask.setCandidateUsers(String.join(",", candidateUsers));
                }
                if (!candidateGroups.isEmpty()) {
                    flowTask.setCandidateGroups(String.join(",", candidateGroups));
                }
            }

            // 获取流程变量作为表单数据
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            if (variables != null && !variables.isEmpty()) {
                flowTask.setFormData(JsonUtils.toJsonString(variables));
            }

            // 设置创建时间
            flowTask.setCreateTime(new Date());

            flowTaskMapper.insert(flowTask);

            log.info("同步任务到flow_task表成功: taskId={}, taskName={}", task.getId(), task.getName());
        }
    }
}
