package com.duoom.workflow.domain.vo;

import com.duoom.common.core.domain.dto.MenuDto;
import com.duoom.workflow.domain.FlowSpecialMenu;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 专项菜单视图对象 flow_special_menu
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@AutoMapper(target = FlowSpecialMenu.class)
public class FlowSpecialMenuVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 功能名称
     */
    private String name;

    /**
     * 功能编码
     */
    private String code;

    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 模板id
     */
    private String schemaId;

    /**
     * 模板权限类型 0 所有 1 指定
     */
    private Long schemaAuthType;

    /**
     * 专项菜单使用人 如果 schema_auth_type == 1 才会有
     */
    private String schemaAuthUserId;

    /**
     * 字段权限 配置 0 所有 1指定
     */
    private Long fieldAuthType;

    /**
     * 专项菜单字段权限 指定成员 如果 field_auth_type == 1 才会有
     */
    private String fieldAuthUserId;

    /**
     * 表单字段配置
     */
    private String fieldConfig;

    /**
     * 查询配置
     */
    private String queryConfig;

    /**
     * 菜单配置不能为空！
     */
    private MenuDto menuInfo;
}
