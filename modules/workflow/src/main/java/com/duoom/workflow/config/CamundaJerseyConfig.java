package com.duoom.workflow.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.ServiceLoader;
import java.util.Iterator;

/**
 * Camunda Jersey配置
 * 解决FastJSON与Jakarta EE的兼容性问题
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "camunda", name = "enabled", havingValue = "true", matchIfMissing = true)
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CamundaJerseyConfig {
    
    static {
        // 禁用FastJSON的JAX-RS自动发现功能
        System.setProperty("fastjson.jaxrs.autoDiscover.disable", "true");
        
        // 移除FastJSON的AutoDiscoverable服务
        try {
            ServiceLoader<?> loader = ServiceLoader.load(
                Class.forName("org.glassfish.jersey.internal.spi.AutoDiscoverable")
            );
            Iterator<?> iterator = loader.iterator();
            while (iterator.hasNext()) {
                Object service = iterator.next();
                if (service.getClass().getName().contains("fastjson")) {
                    iterator.remove();
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 创建一个高优先级的Bean来确保静态初始化块被执行
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public String camundaJerseyConfigMarker() {
        return "CamundaJerseyConfig";
    }
}