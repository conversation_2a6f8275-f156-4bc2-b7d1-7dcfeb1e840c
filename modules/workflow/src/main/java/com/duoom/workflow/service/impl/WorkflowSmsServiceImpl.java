package com.duoom.workflow.service.impl;

import cn.hutool.core.util.StrUtil;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.workflow.domain.dto.TaskUrgeDto;
import com.duoom.workflow.service.WorkflowSmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 工作流短信服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowSmsServiceImpl implements WorkflowSmsService {
    
    /**
     * 阿里云短信模板
     */
    private static final String TEMPLATE_VERIFY_CODE = "SMS_276337229";        // 验证码模板 (参数code)
    private static final String TEMPLATE_SIMPLE_1 = "SMS_487280094";           // 无参数模板1
    private static final String TEMPLATE_SIMPLE_2 = "SMS_487320088";           // 无参数模板2
    private static final String TEMPLATE_PARAMETER = "SMS_487270254";          // 带参数模板 (time,num1,num2,name)
    
    @Override
    public void sendUrgeTaskSms(TaskUrgeDto taskUrgeDto, String assigneePhone) {
        if (StrUtil.isBlank(assigneePhone)) {
            log.warn("处理人手机号为空，无法发送催办短信");
            return;
        }
        
        try {
            // 使用带参数的模板发送催办短信
            // time: 当前时间, name: 催办人, num1: 可以是任务数量等
            String currentTime = java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            String urgeName = "系统用户"; // 这里可以根据实际需求获取催办人姓名
            
            sendParameterSms(assigneePhone, TEMPLATE_PARAMETER, 
                currentTime, "1", "", urgeName);
                
            log.info("催办短信发送成功，接收人：{}, 流程实例：{}", 
                assigneePhone, taskUrgeDto.getProcessInstanceId());
                
        } catch (Exception e) {
            log.error("发送催办短信失败，接收人：{}, 错误信息：{}", assigneePhone, e.getMessage(), e);
            // 这里可以选择是否抛出异常，目前只记录日志不中断业务流程
        }
    }
    
    @Override
    public void sendVerificationCodeSms(String phone, String code) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(code)) {
            throw new ServiceException("手机号或验证码不能为空");
        }
        
        try {
            // 获取短信发送器
            SmsBlend smsBlend = SmsFactory.getSmsBlend("config1");
            
            // 构建参数
            LinkedHashMap<String, String> messages = new LinkedHashMap<>();
            messages.put("code", code);
            
            // 发送短信
            SmsResponse response = smsBlend.sendMessage(phone, TEMPLATE_VERIFY_CODE, messages);
            boolean result = response.isSuccess();
            
            if (result) {
                log.info("验证码短信发送成功，手机号：{}", phone);
            } else {
                log.error("验证码短信发送失败，手机号：{}, 响应：{}", phone, response);
                throw new ServiceException("验证码短信发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送验证码短信异常，手机号：{}, 错误信息：{}", phone, e.getMessage(), e);
            throw new ServiceException("发送验证码短信失败：" + e.getMessage());
        }
    }
    
    @Override
    public void sendSimpleSms(String phone, String templateCode) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(templateCode)) {
            throw new ServiceException("手机号或模板代码不能为空");
        }
        
        try {
            // 获取短信发送器
            SmsBlend smsBlend = SmsFactory.getSmsBlend("config1");
            
            // 发送无参数短信
            SmsResponse response = smsBlend.sendMessage(phone, templateCode);
            boolean result = response.isSuccess();
            
            if (result) {
                log.info("短信发送成功，手机号：{}, 模板：{}", phone, templateCode);
            } else {
                log.error("短信发送失败，手机号：{}, 模板：{}, 响应：{}", phone, templateCode, response);
                throw new ServiceException("短信发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送短信异常，手机号：{}, 模板：{}, 错误信息：{}", phone, templateCode, e.getMessage(), e);
            throw new ServiceException("发送短信失败：" + e.getMessage());
        }
    }
    
    @Override
    public void sendParameterSms(String phone, String templateCode, String time, String num1, String num2, String name) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(templateCode)) {
            throw new ServiceException("手机号或模板代码不能为空");
        }
        
        try {
            // 获取短信发送器
            SmsBlend smsBlend = SmsFactory.getSmsBlend("config1");
            
            // 构建参数 - 按照模板 SMS_487270254 的参数顺序
            LinkedHashMap<String, String> messages = new LinkedHashMap<>();
            if (StrUtil.isNotBlank(time)) {
                messages.put("time", time);
            }
            if (StrUtil.isNotBlank(num1)) {
                messages.put("num1", num1);
            }
            if (StrUtil.isNotBlank(num2)) {
                messages.put("num2", num2);
            }
            if (StrUtil.isNotBlank(name)) {
                messages.put("name", name);
            }
            
            // 发送短信
            SmsResponse response = smsBlend.sendMessage(phone, templateCode, messages);
            boolean result = response.isSuccess();
            
            if (result) {
                log.info("参数短信发送成功，手机号：{}, 模板：{}, 参数：{}", phone, templateCode, messages);
            } else {
                log.error("参数短信发送失败，手机号：{}, 模板：{}, 响应：{}", phone, templateCode, response);
                throw new ServiceException("参数短信发送失败");
            }
            
        } catch (Exception e) {
            log.error("发送参数短信异常，手机号：{}, 模板：{}, 错误信息：{}", phone, templateCode, e.getMessage(), e);
            throw new ServiceException("发送参数短信失败：" + e.getMessage());
        }
    }
}