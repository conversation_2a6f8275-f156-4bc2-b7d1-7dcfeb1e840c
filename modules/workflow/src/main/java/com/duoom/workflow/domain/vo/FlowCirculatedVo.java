package com.duoom.workflow.domain.vo;

import com.duoom.workflow.domain.FlowCirculated;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 流程传阅信息视图对象 flow_circulated
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FlowCirculated.class)
public class FlowCirculatedVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 流程id
     */
    @ExcelProperty(value = "流程id")
    private String processId;

    /**
     * 任务id
     */
    @ExcelProperty(value = "任务id")
    private String taskId;

    /**
     * 任务名
     */
    @ExcelProperty(value = "任务名")
    private String taskName;

    /**
     * 模板id
     */
    @ExcelProperty(value = "模板id")
    private Long schemaId;

    /**
     * 流水号
     */
    @ExcelProperty(value = "流水号")
    private Long serialNumber;

    /**
     * 当前进度
     */
    @ExcelProperty(value = "当前进度")
    private Integer currentProgress;

    /**
     * 发起人id
     */
    @ExcelProperty(value = "发起人id")
    private Long startUserId;

    /**
     * 传阅人的用户id
     */
    @ExcelProperty(value = "传阅人的用户id")
    private Long circulatedUserId;

    /**
     * 是否已读(Y已读；N未读)
     */
    @ExcelProperty(value = "是否已读(Y已读；N未读)")
    private String isRead;

    /**
     * 流程名称
     */
    @ExcelProperty(value = "流程名称")
    private String processName;


}
