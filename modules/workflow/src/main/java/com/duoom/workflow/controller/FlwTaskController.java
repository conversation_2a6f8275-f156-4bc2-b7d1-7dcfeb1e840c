package com.duoom.workflow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.duoom.common.core.domain.R;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.workflow.domain.bo.TodoTaskQueryBo;
import com.duoom.workflow.domain.vo.TodoTaskVo;
import com.duoom.workflow.domain.dto.ApproveDto;
import com.duoom.workflow.domain.dto.BatchApproveDto;
import com.duoom.workflow.domain.dto.TaskUrgeDto;
import com.duoom.workflow.service.IFlwTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

import static com.duoom.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 处理流程任务
 * 审批、撤回、传阅、指派、催办等。
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/task")
public class FlwTaskController extends BaseController {

    private final IFlwTaskService flwTaskService;

    /**
     * 分页查询待办任务
     */
    @GetMapping("/todo/page")
    @Log(title = "查询待办任务", businessType = BusinessType.SELECT)
    @SaCheckPermission("workflow:task:list")
    public TableDataInfo<TodoTaskVo> queryTodoTaskPage(TodoTaskQueryBo queryBo) {
        return flwTaskService.queryTodoTaskPage(queryBo);
    }

    /**
     * 查询待办任务列表
     */
    @GetMapping("/todo/list")
    @Log(title = "查询待办任务", businessType = BusinessType.SELECT)
    @SaCheckPermission("workflow:task:list")
    public R<List<TodoTaskVo>> queryTodoTaskList(TodoTaskQueryBo queryBo) {
        return R.ok(flwTaskService.queryTodoTaskList(queryBo));
    }

    /**
     * 查询待办任务数量
     */
    @GetMapping("/todo/count")
    @Log(title = "查询待办任务数量", businessType = BusinessType.SELECT)
    @SaCheckPermission("workflow:task:list")
    public R<Long> countTodoTask() {
        String userId = getLoginUser().getUserId().toString();
        return R.ok(flwTaskService.countTodoTask(userId));
    }

    /**
     * 获取待办任务详情
     */
    @GetMapping("/todo/{taskId}")
    @Log(title = "查询待办任务详情", businessType = BusinessType.SELECT)
    @SaCheckPermission("workflow:task:query")
    public R<TodoTaskVo> getTodoTaskDetail(@PathVariable String taskId) {
        return R.ok(flwTaskService.getTodoTaskDetail(taskId));
    }

    /**
     * 查询我的待办任务
     */
    @GetMapping("/todo/my")
    @Log(title = "查询我的待办任务", businessType = BusinessType.SELECT)
    @SaCheckPermission("workflow:task:list")
    public R<List<TodoTaskVo>> queryMyTodoTasks() {
        String userId = getLoginUser().getUserId().toString();
        return R.ok(flwTaskService.queryUserTodoTasks(userId));
    }
    
    /**
     * 审批任务
     */
    @PostMapping("/approve")
    @Log(title = "审批任务", businessType = BusinessType.UPDATE)
    @SaCheckPermission("workflow:task:approve")
    public R<Void> approveTask(@Validated @RequestBody ApproveDto dto) {
        flwTaskService.approveTask(dto);
        return R.ok();
    }
    
    /**
     * 批量审批任务
     */
    @PostMapping("/approve/batch")
    @Log(title = "批量审批任务", businessType = BusinessType.UPDATE)
    @SaCheckPermission("workflow:task:approve")
    public R<Void> batchApprove(@Validated @RequestBody BatchApproveDto dto) {
        flwTaskService.batchApprove(dto);
        return R.ok();
    }
    
    /**
     * 认领任务
     */
    @PostMapping("/claim/{taskId}")
    @Log(title = "认领任务", businessType = BusinessType.UPDATE)
    @SaCheckPermission("workflow:task:claim")
    public R<Void> claimTask(@PathVariable String taskId) {
        flwTaskService.claimTask(taskId);
        return R.ok();
    }
    
    /**
     * 取消认领任务
     */
    @PostMapping("/unclaim/{taskId}")
    @Log(title = "取消认领任务", businessType = BusinessType.UPDATE)
    @SaCheckPermission("workflow:task:unclaim")
    public R<Void> unclaimTask(@PathVariable String taskId) {
        flwTaskService.unclaimTask(taskId);
        return R.ok();
    }
    
    /**
     * 获取任务的审批记录
     */
    @GetMapping("/approve-history/{processInstanceId}")
    @Log(title = "查询审批记录", businessType = BusinessType.SELECT)
    @SaCheckPermission("workflow:task:history")
    public R<List<Object>> getApproveHistory(@PathVariable String processInstanceId) {
        return R.ok(flwTaskService.getApproveHistory(processInstanceId));
    }

    /**
     * 催办任务
     * 普通用户（流程发起人）可以催办自己发起的流程
     */
    @PostMapping("/urge")
    @Log(title = "催办任务", businessType = BusinessType.OTHER)
    @SaCheckPermission("workflow:task:urge")
    public R<Void> urgeTask(@Valid @RequestBody TaskUrgeDto dto) {
        flwTaskService.urgeTask(dto);
        return R.ok("催办消息已发送");
    }
}