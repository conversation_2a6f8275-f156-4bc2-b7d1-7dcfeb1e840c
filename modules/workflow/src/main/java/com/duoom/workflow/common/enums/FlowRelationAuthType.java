package com.duoom.workflow.common.enums;

/**
 * @author: Duoom
 */
public enum FlowRelationAuthType {
    /**
     * 限发起人发起的此模板任务
     */
    ORIGINATOR(0, "限发起人发起的此模板任务"),
    /**
     * 所有此模板发起的任务
     */
    ALL(1, "所有此模板发起的任务");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    FlowRelationAuthType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
