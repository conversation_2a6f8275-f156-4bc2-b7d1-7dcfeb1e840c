package com.duoom.workflow.domain.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/29 9:43
 */
@Data
public class MessageConfig {

    /**
     * 模板id
     */
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 参数配置
     */
    private List<MessageAssignmentConfig> configs;


    /**
     * 接收人配置
     */
    private List<ReceiverConfiguration> receiverConfiguration;

}
