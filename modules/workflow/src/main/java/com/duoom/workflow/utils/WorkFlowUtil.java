package com.duoom.workflow.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.duoom.common.core.domain.model.LoginUser;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.core.service.DeptService;
import com.duoom.common.core.utils.SpringUtils;
import com.duoom.common.magicapi.domain.vo.MagicApiInfoVo;
import com.duoom.common.magicapi.service.IMagicApiService;
import com.duoom.common.redis.utils.RedisUtils;
import com.duoom.workflow.common.constant.FlowConstant;
import com.duoom.workflow.common.enums.FlowEventType;
import com.duoom.workflow.common.enums.FlowParamType;
import com.duoom.workflow.domain.config.ApiConfig;
import com.duoom.workflow.domain.config.ApiRequestParamsConfig;
import com.duoom.workflow.domain.config.NodeBasicConfig;
import com.duoom.workflow.domain.config.ProcessParamConfig;
import com.duoom.workflow.domain.vo.FlowSchemaVo;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.*;
import java.util.stream.Stream;

/**
 * 工作流工具类
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@UtilityClass
public class WorkFlowUtil {

    /**
     * 发起流程时， 遍历所有节点 如果有需要监听器的  默认缓存 key -> deployId:ActivityId
     *
     * @param deployId         部署id
     * @param childNodeConfigs 所有节点配置
     */
    public void cacheNodeListener(String deployId, List<Map<String, Object>> childNodeConfigs) {
        if (StrUtil.isBlank(deployId) || CollUtil.isEmpty(childNodeConfigs)) {
            return;
        }

        for (Map<String, Object> nodeMap : childNodeConfigs) {
            NodeBasicConfig nodeConfig = BeanUtil.toBean(nodeMap, NodeBasicConfig.class);

            // 缓存开始事件监听器配置
            if (CollUtil.isNotEmpty(nodeConfig.getStartEventConfigs())) {
                String startKey = buildCacheKey(FlowConstant.START_EVENT_CACHE_PRE, deployId, nodeConfig.getId(), FlowEventType.START.getValue());
                RedisUtils.setCacheList(startKey, nodeConfig.getStartEventConfigs());
            }

            // 缓存结束事件监听器配置
            if (CollUtil.isNotEmpty(nodeConfig.getEndEventConfigs())) {
                String endKey = buildCacheKey(FlowConstant.END_EVENT_CACHE_PRE, deployId, nodeConfig.getId(), FlowEventType.END.getValue());
                RedisUtils.setCacheList(endKey, nodeConfig.getEndEventConfigs());
            }
        }
    }

    /**
     * 构建 Redis 缓存 Key
     *
     * @param prefix     缓存 key 前缀
     * @param deployId   部署 ID
     * @param activityId 节点 ID
     * @param eventType  事件类型
     * @return 构建后的缓存 key
     */
    private String buildCacheKey(String prefix, String deployId, String activityId, String eventType) {
        return new StringBuilder(prefix)
            .append(deployId).append(StringPool.COLON)
            .append(activityId).append(StringPool.COLON)
            .append(eventType).toString();
    }

    /**
     * 删除监听器
     *
     * @param deployId 部署id
     */
    public void removeNodeListener(String deployId) {
        if (StrUtil.isNotBlank(deployId)) {
            RedisUtils.deleteKeys(deployId + StringPool.COLON + "*");
        }
    }

    /**
     * 批量删除监听器
     *
     * @param deployId 部署id
     */
    public void removeNodeListener(List<String> deployId) {
        if (CollUtil.isNotEmpty(deployId)) {
            deployId.forEach(WorkFlowUtil::removeNodeListener);
        }
    }

    /**
     * 获取流程参数
     *
     * @param processParamConfigs
     * @param workflowSchema
     * @return
     */
    @SneakyThrows
    public static Map<String, Object> getProcessParam(
        List<ProcessParamConfig> processParamConfigs,
        FlowSchemaVo workflowSchema,
        Long serialNumber,
        Map<String, Object> varMap,
        LoginUser loginUser) {

        if (CollUtil.isEmpty(processParamConfigs)) {
            return Collections.emptyMap();
        }

        Map<String, Object> result = new HashMap<>(processParamConfigs.size());
        IMagicApiService magicApiService = SpringUtils.getBean(IMagicApiService.class);
        MagicAPIService magicAPIService = SpringUtils.getBean(MagicAPIService.class);

        for (ProcessParamConfig config : processParamConfigs) {
            String id = config.getId();
            String value = config.getValue();
            FlowParamType type = FlowParamType.getByCode(config.getType());

            switch (type) {
                case VALUE -> result.put(id, value);

                case VAR -> {
                    String replaced = PlaceholderUtil.replacePlaceHolder(value, workflowSchema, serialNumber, 0L, loginUser);
                    result.put(id, replaced);
                }

                case API -> {
                    ApiConfig apiConfig = config.getApiConfig();
                    MagicApiInfoVo info = magicApiService.info(apiConfig.getId());
                    Map<String, Object> params = new HashMap<>();

                    // 参数处理三类
                    List<ApiRequestParamsConfig> allParams = Stream.of(
                        apiConfig.getRequestParamsConfigs(),
                        apiConfig.getRequestHeaderConfigs(),
                        apiConfig.getRequestBodyConfigs()
                    ).flatMap(Collection::stream).toList();

                    for (ApiRequestParamsConfig p : allParams) {
                        String paramName = p.getName();
                        String assignmentType = p.getAssignmentType();

                        Object paramValue = switch (assignmentType) {
                            case "value" -> p.getValue();
                            case "originator" -> dealApiParamAndReturn(p, varMap, loginUser);
                            default ->
                                PlaceholderUtil.replacePlaceHolder(value, workflowSchema, serialNumber, 0L, loginUser);
                        };

                        params.put(paramName, paramValue);
                    }

                    Object executeResult = magicAPIService.execute(info.getMethod(), info.getPath(), params);
                    result.put(id, executeResult);
                }
                default -> throw new ServiceException("流程类型错误：" + config.getType());
            }
        }

        return result;
    }

    /**
     * 提取封装一个处理 originator 的返回逻辑，保持主方法简洁
     *
     * @param p
     * @param varMap
     * @param loginUser
     * @return
     */
    private static Object dealApiParamAndReturn(ApiRequestParamsConfig p, Map<String, Object> varMap, LoginUser loginUser) {
        Map<String, Object> tempMap = new HashMap<>();
        dealApiParam(p, tempMap, varMap, loginUser);
        return tempMap.get(p.getName());
    }


    /**
     * 处理 API 参数：将发起人信息（如用户ID、部门名称）根据流程节点上下文设置进参数 Map 中
     *
     * @param requestParamsConfig 请求参数配置（包含参数名和配置项）
     * @param params              最终要提交的参数 Map
     * @param varMap              流程变量 Map（为空代表流程发起阶段）
     * @param user                当前登录用户信息
     */
    private void dealApiParam(ApiRequestParamsConfig requestParamsConfig,
                              Map<String, Object> params,
                              Map<String, Object> varMap,
                              LoginUser user) {
        String paramName = requestParamsConfig.getName();

        // 如果 varMap 为空，表示是流程发起阶段（或重新发起）
        if (CollUtil.isEmpty(varMap)) {
            // 发起人ID配置
            if (StrUtil.contains(requestParamsConfig.getConfig(), "initiator_id") && user != null) {
                params.put(paramName, ObjUtil.defaultIfNull(user.getUserId(), StringPool.EMPTY));
            }

            // 发起人部门名配置
            if (StrUtil.contains(requestParamsConfig.getConfig(), "initiator_dept_name") && user != null) {
                params.put(paramName, StrUtil.nullToEmpty(user.getDeptName()));
            }

        } else {
            // 从流程变量中获取发起人ID（注意空值保护）
            Long startUserId = MapUtil.get(varMap, FlowConstant.PROCESS_START_USER_ID_KEY, Long.class);
            if (startUserId != null) {
                // 发起人ID配置
                if (StrUtil.contains(requestParamsConfig.getConfig(), "initiator_id")) {
                    params.put(paramName, startUserId);
                }

                // 发起人部门名配置
                if (StrUtil.contains(requestParamsConfig.getConfig(), "initiator_dept_name")) {
                    DeptService deptService = SpringUtils.getBean(DeptService.class);
                    String deptName = deptService.selectDeptNameByUserIds(startUserId.toString());
                    params.put(paramName, StrUtil.nullToEmpty(deptName));
                }
            } else {
                // 若流程变量中缺失发起人ID
                params.put(paramName, StringPool.EMPTY);
            }
        }
    }
}
