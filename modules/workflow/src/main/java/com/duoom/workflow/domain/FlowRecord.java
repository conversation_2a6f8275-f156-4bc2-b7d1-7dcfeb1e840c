package com.duoom.workflow.domain;

import com.duoom.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 工作流 流转记录信息对象 flow_record
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flow_record")
public class FlowRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点多实例类型（节点审批类型）
     */
    private Integer nodeMultiType;

    /**
     * 模板id
     */
    private Long schemaId;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 审批信息
     */
    private String message;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 传阅信息
     */
    private String circulateMessage;

    /**
     *
     */
    private Long createUserId;


}
