package com.duoom.workflow.domain;

import com.duoom.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 专项菜单对象 flow_special_menu
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flow_special_menu")
public class FlowSpecialMenu extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 功能名称
     */
    private String name;

    /**
     * 功能编码
     */
    private String code;

    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 模板id
     */
    private String schemaId;

    /**
     * 模板权限类型 0 所有 1 指定
     */
    private Long schemaAuthType;

    /**
     * 专项菜单使用人 如果 schema_auth_type == 1 才会有
     */
    private String schemaAuthUserId;

    /**
     * 字段权限 配置 0 所有 1指定
     */
    private Long fieldAuthType;

    /**
     * 专项菜单字段权限 指定成员 如果 field_auth_type == 1 才会有
     */
    private String fieldAuthUserId;

    /**
     * 表单字段配置
     */
    private String fieldConfig;

    /**
     * 查询配置
     */
    private String queryConfig;


}
