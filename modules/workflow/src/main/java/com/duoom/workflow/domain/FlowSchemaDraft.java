package com.duoom.workflow.domain;

import com.duoom.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 流程模板对象 flow_schema_draft
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flow_schema_draft")
public class FlowSchemaDraft extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 流程编码
     */
    private String code;

    /**
     * 流程模板名称
     */
    private String name;

    /**
     * 模板内容
     */
    private String xmlContent;

    /**
     * 模板Json
     */
    private String jsonContent;

    /**
     * 是否删除
     */
    private Integer deleteMark;

    /**
     * 状态（Y正常 N无效）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
