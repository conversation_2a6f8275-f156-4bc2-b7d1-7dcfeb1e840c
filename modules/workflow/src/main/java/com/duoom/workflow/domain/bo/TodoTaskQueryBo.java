package com.duoom.workflow.domain.bo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 待办任务查询业务对象
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@Data
public class TodoTaskQueryBo implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer pageSize;

    private Integer pageNum;

    private String processName;

    private String categoryId;

    private String processTitle;

    private String taskName;

    private String startUserId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskCreateTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskCreateTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeBegin;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeEnd;

    private Integer urgency;

    private Boolean onlyMyStart;

    private Boolean onlyAssignedToMe;

    private Boolean includeDelegated;

    private String taskStatus;

    private String orderByColumn;

    private String isAsc;
}
