package com.duoom.workflow.utils;

import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;

/**
 * 流程XML处理工具类
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
public class ProcessXmlUtil {

    private static final String BPMN_NAMESPACE = "http://www.omg.org/spec/BPMN/20100524/MODEL";

    /**
     * 更新XML中的process id
     *
     * @param xmlContent 原始XML内容
     * @param newProcessId 新的process id
     * @return 更新后的XML内容
     */
    public static String updateProcessId(String xmlContent, String newProcessId) {
        try {
            // 解析 XML
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xmlContent.getBytes(StandardCharsets.UTF_8)));

            // 找到 process 元素
            Element processElement = (Element) doc.getElementsByTagNameNS(BPMN_NAMESPACE, "process").item(0);
            if (processElement != null) {
                String oldId = processElement.getAttribute("id");
                log.info("更新 process id: {} -> {}", oldId, newProcessId);
                
                // 更新 process id
                processElement.setAttribute("id", newProcessId);
                
                // 确保 isExecutable 属性为 true
                String isExecutable = processElement.getAttribute("isExecutable");
                if (!"true".equals(isExecutable)) {
                    log.info("设置 isExecutable 为 true (原值: {})", isExecutable);
                    processElement.setAttribute("isExecutable", "true");
                }
                
                // 同时更新 BPMNPlane 的 bpmnElement 属性
                Element bpmnPlane = (Element) doc.getElementsByTagNameNS("http://www.omg.org/spec/BPMN/20100524/DI", "BPMNPlane").item(0);
                if (bpmnPlane != null) {
                    bpmnPlane.setAttribute("bpmnElement", newProcessId);
                }
            }

            // 将修改后的 XML 转回字符串
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(OutputKeys.INDENT, "no");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            
            return writer.toString();
        } catch (Exception e) {
            log.error("更新 process id 失败", e);
            return xmlContent; // 如果失败，返回原始内容
        }
    }

    /**
     * 从XML中获取process id
     *
     * @param xmlContent XML内容
     * @return process id，如果获取失败返回null
     */
    public static String getProcessId(String xmlContent) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xmlContent.getBytes(StandardCharsets.UTF_8)));

            Element processElement = (Element) doc.getElementsByTagNameNS(BPMN_NAMESPACE, "process").item(0);
            if (processElement != null) {
                return processElement.getAttribute("id");
            }
        } catch (Exception e) {
            log.error("获取 process id 失败", e);
        }
        return null;
    }

    /**
     * 将code转换为合法的process id
     * BPMN规范要求process id必须符合XML NCName规范
     *
     * @param code 流程代码
     * @return 合法的process id
     */
    public static String codeToProcessId(String code) {
        if (code == null) {
            return null;
        }
        // 替换不合法字符
        return code.replaceAll("[^a-zA-Z0-9_]", "_");
    }
}