package com.duoom.workflow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.duoom.common.core.domain.R;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.workflow.domain.bo.DraftQueryBo;
import com.duoom.workflow.domain.bo.FlowDraftBo;
import com.duoom.workflow.domain.vo.FlowDraftVo;
import com.duoom.workflow.service.IFlwDraftService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程草稿
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/deploy")
public class FlwDraftController extends BaseController {

    private final IFlwDraftService workflowDraftService;

    /**
     * 查询流程草稿列表
     */
    @SaCheckPermission("workflow:draft:list")
    @GetMapping("/list")
    public TableDataInfo<FlowDraftVo> list(DraftQueryBo bo, PageQuery pageQuery) {
        return workflowDraftService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取流程草稿详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("workflow:draft:query")
    @GetMapping("/{id}")
    public R<FlowDraftVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(workflowDraftService.queryById(id));
    }

    /**
     * 新增流程草稿
     */
    @SaCheckPermission("workflow:draft:add")
    @Log(title = "流程草稿", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FlowDraftBo bo) {
        return toAjax(workflowDraftService.insertByBo(bo));
    }

    /**
     * 修改流程草稿
     */
    @SaCheckPermission("workflow:draft:edit")
    @Log(title = "流程草稿", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FlowDraftBo bo) {
        return toAjax(workflowDraftService.updateByBo(bo));
    }

    /**
     * 删除流程草稿
     *
     * @param ids 主键串
     */
    @SaCheckPermission("workflow:draft:remove")
    @Log(title = "流程草稿", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(workflowDraftService.deleteWithValidByIds(List.of(ids), true));
    }
}
