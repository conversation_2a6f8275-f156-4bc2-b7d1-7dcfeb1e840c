package com.duoom.workflow.domain.config;

import lombok.Data;

import java.util.List;

/**
 * 会签配置
 * @author: Duoom
 * @Date: 2022/9/23 9:41
 */
@Data
public class CountersignConfig {
    /**
     * 多实例类型 0 无 1 同步执行  2 异步执行
     */
    private Integer multipleInstancesType = 0;

    /**
     * 加签 减签
     */
    private Boolean addOrRemove;

    /**
     * 完成条件 0 全部  1 单个 2百分比
     */
    private Integer finishType;

    /**
     * 完成百分比
     */
    private Integer percentage;

    /**
     * 会签 人员配置
     */
    private List<CountersignMemberConfig> countersignList;

}
