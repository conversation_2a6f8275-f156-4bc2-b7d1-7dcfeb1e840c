package com.duoom.workflow.domain.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/29 10:02
 */
@Data
public class MessageAssignmentConfig {
    /**
     *参数名称
     */
    private String name;

    /**
     * 参数描述
     */
    private String description;

    /**
     * 参数类型，formData表单数据、value值
     */
    private String assignmentType;

    /**
     * assignmentType = value时，参数value值信息
     */
    private String value;

    /**
     * assignmentType = formData时，参数formData表单数据的配置信息
     */
    private String config;
}
