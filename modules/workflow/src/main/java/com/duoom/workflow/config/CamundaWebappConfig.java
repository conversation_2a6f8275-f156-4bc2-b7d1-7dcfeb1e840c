package com.duoom.workflow.config;

import org.camunda.bpm.spring.boot.starter.annotation.EnableProcessApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Camunda Web应用配置
 * 启用Camunda的Web应用，包括Cockpit、Tasklist和Admin
 *
 * <AUTHOR>
 */
@Configuration
@EnableProcessApplication
@ConditionalOnProperty(prefix = "camunda", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CamundaWebappConfig {

    /**
     * Camunda Web应用默认会在以下路径启用：
     * - /camunda/app/cockpit - 流程监控中心
     * - /camunda/app/tasklist - 任务列表
     * - /camunda/app/admin - 管理中心
     * - /camunda/app/welcome - 欢迎页面
     * 
     * 默认用户名密码在application.yml中配置：
     * camunda.bpm.admin-user.id=admin
     * camunda.bpm.admin-user.password=admin
     */
}