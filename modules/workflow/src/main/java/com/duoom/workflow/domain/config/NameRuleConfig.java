package com.duoom.workflow.domain.config;

import lombok.Data;

/**
 * @author: Duoom
 * @Date: 2023/4/11 19:19
 */
@Data
public class NameRuleConfig {

    private String key;

    private String title;
    
    /**
     * 类型：text-文本, date-日期, serial-序号
     */
    private String type;
    
    /**
     * 值（当type为text时使用）
     */
    private String value;
    
    /**
     * 日期格式（当type为date时使用）
     */
    private String format;
    
    /**
     * 序号长度（当type为serial时使用）
     */
    private Integer length;

}
