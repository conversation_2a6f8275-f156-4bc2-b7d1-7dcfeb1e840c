package com.duoom.liteflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.core.utils.MapstructUtils;
import com.duoom.common.core.utils.StringUtils;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.liteflow.domain.LiteflowCode;
import com.duoom.liteflow.domain.bo.LiteflowCodeBo;
import com.duoom.liteflow.domain.vo.LiteflowCodeVo;
import com.duoom.liteflow.mapper.LiteflowCodeMapper;
import com.duoom.liteflow.service.ILiteflowCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 规则代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LiteflowCodeServiceImpl implements ILiteflowCodeService {

    private final LiteflowCodeMapper baseMapper;

    /**
     * 查询规则代码
     *
     * @param id 主键
     * @return 规则代码
     */
    @Override
    public LiteflowCodeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询规则代码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 规则代码分页列表
     */
    @Override
    public TableDataInfo<LiteflowCodeVo> queryPageList(LiteflowCodeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiteflowCode> lqw = buildQueryWrapper(bo);
        Page<LiteflowCodeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的规则代码列表
     *
     * @param bo 查询条件
     * @return 规则代码列表
     */
    @Override
    public List<LiteflowCodeVo> queryList(LiteflowCodeBo bo) {
        LambdaQueryWrapper<LiteflowCode> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiteflowCode> buildQueryWrapper(LiteflowCodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiteflowCode> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiteflowCode::getId);
        lqw.like(StringUtils.isNotBlank(bo.getMethodName()), LiteflowCode::getMethodName, bo.getMethodName());
        lqw.eq(StringUtils.isNotBlank(bo.getCodeNumber()), LiteflowCode::getCodeNumber, bo.getCodeNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getCodeData()), LiteflowCode::getCodeData, bo.getCodeData());
        return lqw;
    }

    /**
     * 新增规则代码
     *
     * @param bo 规则代码
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiteflowCodeBo bo) {
        LiteflowCode add = MapstructUtils.convert(bo, LiteflowCode.class);
        validEntityBeforeSave(add);
        // 校验 & 提取代码基础信息
        String codeData = bo.getCodeData();
        if (StrUtil.isBlank(codeData)) {
            throw new ServiceException("代码内容不能为空");
        }

        FileMeta fileMeta = parseFileMeta(codeData);

        // 生成 Java 文件
        writeCodeFile(fileMeta, codeData);

        // 插入数据库
        boolean success = baseMapper.insert(add) > 0;
        if (success) {
            bo.setId(add.getId());
        }
        return success;
    }

    /**
     * 修改规则代码
     *
     * @param bo 规则代码
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiteflowCodeBo bo) {
        LiteflowCode update = MapstructUtils.convert(bo, LiteflowCode.class);
        validEntityBeforeSave(update);
        // 查询旧记录
        LiteflowCode oldCode = baseMapper.selectById(bo.getId());
        if (oldCode == null) {
            throw new ServiceException("待修改记录不存在");
        }

        // 获取旧文件路径信息
        FileMeta oldMeta = parseFileMeta(oldCode.getCodeData());
        String oldFilePath = buildFilePath(oldMeta);

        // 删除旧文件（如果存在）
        File oldFile = new File(oldFilePath);
        if (oldFile.exists()) {
            boolean deleted = oldFile.delete();
            if (deleted) {
                log.info("旧 Java 文件已删除：{}", oldFilePath);
            } else {
                log.warn("旧 Java 文件删除失败：{}", oldFilePath);
            }
        }

        // 写入新文件
        String newCodeData = bo.getCodeData();
        FileMeta newMeta = parseFileMeta(newCodeData);
        writeCodeFile(newMeta, newCodeData);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除规则代码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(CollUtil.isEmpty(ids)){
            return Boolean.FALSE;
        }
        //先根据id查出之前的数据
        List<LiteflowCode> liteflowCodeList = baseMapper.selectByIds(ids);
        liteflowCodeList.forEach(liteflowCode -> {
            FileMeta fileMeta = parseFileMeta(liteflowCode.getCodeData());
            String filePath = buildFilePath(fileMeta);
            if (FileUtil.del(filePath)) {
                log.info("Java 文件已删除：{}", filePath);
            } else {
                log.warn("Java 文件删除失败：{}", filePath);
            }
        });
        return baseMapper.deleteByIds(ids) > 0;
    }

    /* ----------------------------------------
     * 公共方法区域（提取重复逻辑）
     * ---------------------------------------- */

    /**
     * Java 源码文件元数据（类名 + 包路径）
     */
    private record FileMeta(String className, String packagePath) {}

    /**
     * 解析源码字符串，提取类名和包路径
     */
    private FileMeta parseFileMeta(String code) {
        String className = StringUtils.getClassNameFromCode(code);
        String packagePath = StringUtils.getPackageNameInFirstLine(code);

        if (StrUtil.hasBlank(className, packagePath)) {
            throw new ServiceException("类名或包名解析失败");
        }
        return new FileMeta(className, packagePath);
    }

    /**
     * 构造完整 Java 文件路径
     */
    private String buildFilePath(FileMeta meta) {
        return StrUtil.join(StringPool.SLASH,
            System.getProperty("user.dir"), "src", "main", "java",
            meta.packagePath(), meta.className() + StringPool.DOT_JAVA
        );
    }

    /**
     * 写入 Java 文件（自动创建目录，已有则备份）
     */
    private void writeCodeFile(FileMeta meta, String content) {
        String dirPath = StrUtil.join(StringPool.SLASH,
            System.getProperty("user.dir"), "src", "main", "java", meta.packagePath()
        );
        String fileName = meta.className() + StringPool.DOT_JAVA;
        String fullPath = dirPath + StringPool.SLASH + fileName;

        File dir = new File(dirPath);
        if (!dir.exists() && !dir.mkdirs()) {
            throw new ServiceException("创建目录失败：" + dirPath);
        }

        File targetFile = new File(fullPath);
        if (targetFile.exists()) {
            String backupName = fileName + "." + System.currentTimeMillis() + ".bak";
            FileUtil.rename(targetFile, backupName, false);
        }

        try (FileOutputStream out = new FileOutputStream(fullPath)) {
            IoUtil.write(out, true, content.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            throw new ServiceException("写入 Java 文件失败：" + e.getMessage());
        }
    }
    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiteflowCode entity){
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<LiteflowCode>()
            .eq(LiteflowCode::getMethodName, entity.getMethodName())
            .or(w->w.eq(LiteflowCode::getCodeNumber, entity.getCodeNumber()))
            .ne(ObjUtil.isNotNull(entity.getId()),LiteflowCode::getId, entity.getId()));
        if(exist){
            throw new ServiceException("规则方法名或者代码编号已存在！");
        }
    }
}
