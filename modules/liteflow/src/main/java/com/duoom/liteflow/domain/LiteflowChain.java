package com.duoom.liteflow.domain;

import com.duoom.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 规则对象 liteflow_chain
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("liteflow_chain")
public class LiteflowChain extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 应用名
     */
    private String applicationName;

    /**
     * 规则名
     */
    private String chainName;

    /**
     * 备注
     */
    private String chainDesc;

    /**
     * 规则文件
     */
    private String elData;


}
