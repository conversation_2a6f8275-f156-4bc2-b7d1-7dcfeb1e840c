package com.duoom.liteflow.service;

import com.duoom.liteflow.domain.vo.LiteflowChainVo;
import com.duoom.liteflow.domain.bo.LiteflowChainBo;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 规则Service接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ILiteflowChainService {

    /**
     * 查询规则
     *
     * @param id 主键
     * @return 规则
     */
    LiteflowChainVo queryById(Long id);

    /**
     * 分页查询规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 规则分页列表
     */
    TableDataInfo<LiteflowChainVo> queryPageList(LiteflowChainBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的规则列表
     *
     * @param bo 查询条件
     * @return 规则列表
     */
    List<LiteflowChainVo> queryList(LiteflowChainBo bo);

    /**
     * 新增规则
     *
     * @param bo 规则
     * @return 是否新增成功
     */
    Boolean insertByBo(LiteflowChainBo bo);

    /**
     * 修改规则
     *
     * @param bo 规则
     * @return 是否修改成功
     */
    Boolean updateByBo(LiteflowChainBo bo);

    /**
     * 校验并批量删除规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 执行规则
     *
     * @param bo 执行参数
     * @return 是否执行成功
     */
    Boolean execute(LiteflowChainBo bo);
}
