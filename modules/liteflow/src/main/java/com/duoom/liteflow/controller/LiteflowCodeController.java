package com.duoom.liteflow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.duoom.common.core.domain.R;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.liteflow.domain.bo.LiteflowCodeBo;
import com.duoom.liteflow.domain.vo.LiteflowCodeVo;
import com.duoom.liteflow.service.ILiteflowCodeService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 规则代码
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/liteflow/code")
public class LiteflowCodeController extends BaseController {

    private final ILiteflowCodeService liteflowCodeService;

    /**
     * 查询规则代码列表
     */
    @SaCheckPermission("liteflow:code:list")
    @GetMapping("/list")
    public TableDataInfo<LiteflowCodeVo> list(LiteflowCodeBo bo, PageQuery pageQuery) {
        return liteflowCodeService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取规则代码详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("liteflow:code:query")
    @GetMapping("/{id}")
    public R<LiteflowCodeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liteflowCodeService.queryById(id));
    }

    /**
     * 新增规则代码
     */
    @SaCheckPermission("liteflow:code:add")
    @Log(title = "规则代码", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiteflowCodeBo bo) {
        return toAjax(liteflowCodeService.insertByBo(bo));
    }

    /**
     * 修改规则代码
     */
    @SaCheckPermission("liteflow:code:edit")
    @Log(title = "规则代码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiteflowCodeBo bo) {
        return toAjax(liteflowCodeService.updateByBo(bo));
    }

    /**
     * 删除规则代码
     *
     * @param ids 主键串
     */
    @SaCheckPermission("liteflow:code:remove")
    @Log(title = "规则代码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liteflowCodeService.deleteWithValidByIds(List.of(ids), true));
    }
}
