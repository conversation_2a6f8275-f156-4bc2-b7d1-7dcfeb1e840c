package com.duoom.liteflow.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.duoom.liteflow.domain.LiteflowChain;
import com.duoom.liteflow.domain.vo.LiteflowChainVo;
import com.duoom.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;
import java.util.List;

/**
 * 规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface LiteflowChainMapper extends BaseMapperPlus<LiteflowChain, LiteflowChainVo> {

    /**
     * 根据id查询名称
     *
     * @param ids
     * @return
     */
    default String[] selectNamesByIds(Collection<Long> ids){
        if(ids.isEmpty()){
            return new String[0];
        }
        List<LiteflowChain> liteflowChains = selectList(new LambdaQueryWrapper<LiteflowChain>()
                .select(LiteflowChain::getChainName)
                .in(LiteflowChain::getId, ids));
        return liteflowChains.stream().map(LiteflowChain::getChainName).toArray(String[]::new);
    }
}
