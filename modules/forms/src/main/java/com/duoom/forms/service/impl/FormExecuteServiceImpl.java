package com.duoom.forms.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.*;
import cn.hutool.db.dialect.DialectFactory;
import cn.hutool.db.meta.Column;
import cn.hutool.db.meta.JdbcType;
import cn.hutool.db.meta.MetaUtil;
import cn.hutool.db.meta.Table;
import cn.hutool.db.sql.Direction;
import cn.hutool.db.sql.Order;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.duoom.common.core.enums.StatusEnum;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.satoken.utils.LoginHelper;
import com.duoom.forms.constant.ComponentTypeConstant;
import com.duoom.forms.constant.FormConstant;
import com.duoom.forms.domain.FormRelease;
import com.duoom.forms.domain.FormTemplate;
import com.duoom.forms.domain.bo.FormExecuteBo;
import com.duoom.forms.domain.bo.FormExecuteDeleteBo;
import com.duoom.forms.domain.bo.FormExecuteListBo;
import com.duoom.forms.domain.bo.FormExecuteWorkflowInfoBo;
import com.duoom.forms.domain.config.*;
import com.duoom.forms.enums.DeleteMark;
import com.duoom.forms.enums.OracleFieldsType;
import com.duoom.forms.handler.DuoomEntityHandler;
import com.duoom.forms.handler.DuoomEntityListHandler;
import com.duoom.forms.mapper.FormReleaseMapper;
import com.duoom.forms.mapper.FormTemplateMapper;
import com.duoom.forms.service.ICodeRuleService;
import com.duoom.forms.service.IFormExecuteService;
import com.duoom.forms.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.TimestampValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.util.SelectUtils;
import net.sf.jsqlparser.util.cnfexpression.MultiAndExpression;
import net.sf.jsqlparser.util.cnfexpression.MultiOrExpression;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class FormExecuteServiceImpl implements IFormExecuteService {

    private final FormReleaseMapper formReleaseMapper;

    private final FormTemplateMapper formTemplateMapper;

    private final ICodeRuleService codeRuleService;

    /**
     * 需要模糊查询的
     */
    private final List<String> LIKE_CLASS_NAME = Collections.singletonList("String");

    /**
     * 需要完全对比判断
     */
    private final List<String> EQ_CLASS_NAME = Arrays.asList("Integer", "Long", "Double", "Float", "Boolean");

    /**
     * 时间类型
     */
    private final List<String> TIME_CLASS_NAME = Collections.singletonList("LocalDateTime");

    /**
     * 自定义表单-根据配置获取数据-列表
     */
    @Override
    public TableDataInfo<Entity> list(FormExecuteListBo bo, PageQuery pageQuery) {

        FormRelease formRelease = formReleaseMapper.selectById(bo.getReleaseId());

        String configJson = formRelease.getConfigJson();

        //发布配置
        FormReleaseConfig formReleaseConfig = JSONUtil.toBean(configJson, FormReleaseConfig.class);

        //自定义表单数据
        FormTemplate template = formTemplateMapper.selectById(formRelease.getFormId());

        String formJson = template.getFormJson();
        //自定义表单配置
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formJson, FormDesignConfig.class);

        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();

        //主表
        Optional<TableConfig> mainTable = tableConfigs.stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTable.isPresent()) {
            TableConfig tableConfig = mainTable.get();
            String tableName = tableConfig.getTableName();

            //构建分页参数
            cn.hutool.db.Page page = new Page(pageQuery.getPageNum() - 1, pageQuery.getPageSize());
            String field = pageQuery.getOrderByColumn();
            String orderStr = pageQuery.getIsAsc();
            ListConfig listConfig = formReleaseConfig.getListConfig();
            if (StrUtil.isBlank(field)) {
                field = StrUtil.emptyToDefault(listConfig.getOrderBy(), tableConfig.getPkField());
                orderStr = StrUtil.emptyToDefault(listConfig.getOrderType(), "desc");
            }
            if (StrUtil.isNotBlank(field)) {
                Order order = new Order();
                order.setDirection(StrUtil.equalsIgnoreCase(orderStr, "desc") ? Direction.DESC : Direction.ASC);
                order.setField(field);
                page.setOrder(order);
            }

            List<ColumnConfig> columnConfigs = listConfig.getColumnConfigs();
            Set<String> fieldsList = columnConfigs.stream().map(ColumnConfig::getColumnName).collect(Collectors.toSet());
            // 添加权限所属人字段返回
            if (BooleanUtils.isTrue(formDesignConfig.getIsDataAuth())) {
                fieldsList.add(FormConstant.AUTH_USER_ID);
            }

            TableDataInfo<Entity> pageData = getPageDataByExpression(tableName, fieldsList, formDesignConfig, formReleaseConfig, bo.getParams(), page);
            if (bo.getIsTrans()) {
                // 关联数据显示转换
                FormDataTransUtil.transData(pageData.getRows(), formDesignConfig);
            }
            return pageData;
        } else {
            throw new ServiceException("主表不存在");
        }
    }

    /**
     * 根据配置信息获取分页列表数据
     *
     * @param tableName         主表名
     * @param fieldsList        列表所有字段名
     * @param formDesignConfig  表单配置
     * @param formReleaseConfig 表单发布配置
     * @param params            入参
     * @param page              分页参数
     * @return 列表数据
     */
    @SneakyThrows
    private TableDataInfo<Entity> getPageDataByExpression(String tableName, Set<String> fieldsList, FormDesignConfig formDesignConfig, FormReleaseConfig formReleaseConfig, Map<String, Object> params, cn.hutool.db.Page page) {
        DataSource datasource = DatasourceUtil.getDataSource(formDesignConfig.getDatabaseId());
        //获取表里所有字段
        Table tableMeta = MetaUtil.getTableMeta(datasource, tableName);
        Collection<Column> columns = tableMeta.getColumns();

        Optional<Column> pkOptional = columns.stream().filter(Column::isPk).findFirst();
        Column pkColumn;

        if (pkOptional.isPresent()) {
            pkColumn = pkOptional.get();
            fieldsList.add(pkColumn.getName());
        } else {
            throw new ServiceException("主键不存在");
        }


        List<net.sf.jsqlparser.schema.Column> jsqlColumn = new ArrayList<>();
        for (String field : fieldsList) {
            jsqlColumn.add(new net.sf.jsqlparser.schema.Column(field));
        }
        Select select = SelectUtils.buildSelectFromTableAndExpressions(new net.sf.jsqlparser.schema.Table(tableName), jsqlColumn.toArray(new net.sf.jsqlparser.schema.Column[0]));
        PlainSelect plainSelect = select.getPlainSelect(); // 转换为更细化的Select对象

        List<QueryConfig> queryConfigs = formReleaseConfig.getListConfig().getQueryConfigs();

        Expression queryExpression = null;

        //如果有左侧树 需要把所选项目 where 条件加上
        if (formReleaseConfig.getListConfig().getIsLeftMenu()) {

            List<Map<String, Object>> conditions = (List<Map<String, Object>>) params.get("treeConditions");
            if (CollectionUtils.isNotEmpty(conditions)) {
                List<Expression> orList = new ArrayList<>(conditions.size());
                for (Map<String, Object> condition : conditions) {
                    List<Expression> andList = new ArrayList<>(condition.size());
                    for (Map.Entry<String, Object> entry : condition.entrySet()) {
                        Object value = entry.getValue();
                        if (ObjectUtils.isEmpty(value)) {
                            continue;
                        }
                        EqualsTo eq = new EqualsTo();
                        eq.setLeftExpression(new net.sf.jsqlparser.schema.Column(entry.getKey()));
                        if (value instanceof Integer || value instanceof Long) {
                            eq.setRightExpression(new LongValue(Long.parseLong(value.toString())));
                        } else {
                            eq.setRightExpression(new StringValue(value.toString()));
                        }
                        andList.add(eq);
                    }
                    orList.add(new MultiAndExpression(andList));
                }
                queryExpression = new ParenthesedExpressionList<>(new MultiOrExpression(orList));
            }
        }

        //遍历所有查询条件
        for (QueryConfig queryConfig : queryConfigs) {
            String fieldName = queryConfig.getFieldName();
            //如果是日期类型 默认设置查询参数为两个
            if (queryConfig.getIsDate()) {
                //根据查询配置的字段名 从参数里面找到对应的值  如果是时间类型 需要查询start 和 end
                String startTime = MapUtil.get(params, fieldName + FormConstant.START_TIME_SUFFIX, String.class);
                String endTime = MapUtil.get(params, fieldName + FormConstant.END_TIME_SUFFIX, String.class);

                //如果全都没有数据 则跳过
                if (startTime == null && endTime == null) {
                    continue;
                }
                Expression startRightExp = null;
                Expression endRightExp = null;
                Column queryColumn = columns.stream().filter(c -> StrUtil.equalsIgnoreCase(c.getName(), fieldName)).findFirst().get();
                JdbcType type = queryColumn.getTypeEnum();
                if (type == JdbcType.TIME) {
                    if (StrUtil.isNotEmpty(startTime)) startRightExp = new StringValue(startTime);
                    if (StrUtil.isNotEmpty(endTime)) endRightExp = new StringValue(endTime);
                } else if (type == JdbcType.DATE || type == JdbcType.TIMESTAMP) {
                    if (StrUtil.isNotEmpty(startTime))
                        startRightExp = new TimestampValue().withValue(Timestamp.valueOf(Objects.requireNonNull(LocalDateTimeUtil.parseDateByLength(startTime))));
                    if (StrUtil.isNotEmpty(endTime))
                        endRightExp = new TimestampValue().withValue(Timestamp.valueOf(Objects.requireNonNull(LocalDateTimeUtil.parseDateByLength(endTime))));
                } else if (StrUtil.equalsIgnoreCase(queryColumn.getTypeName(), OracleFieldsType.TIME.getType())) {
                    // oracle时间字段处理
                    if (StrUtil.isNotEmpty(startTime))
                        startRightExp = new StringValue(StringPool.ZERO + StringPool.SPACE + startTime);
                    if (StrUtil.isNotEmpty(endTime))
                        endRightExp = new StringValue(StringPool.ZERO + StringPool.SPACE + endTime);
                }
                if (startTime != null) {
                    GreaterThanEquals geq = new GreaterThanEquals(); // ">="
                    geq.setLeftExpression(new net.sf.jsqlparser.schema.Column(fieldName));
                    geq.setRightExpression(startRightExp == null ? new StringValue(startTime) : startRightExp);

                    //如果是第一个条件 直接赋值
                    if (ObjectUtil.isNull(queryExpression)) {
                        queryExpression = geq;
                    } else {
                        queryExpression = new AndExpression(queryExpression, geq);
                    }
                }

                if (endTime != null) {
                    MinorThanEquals leq = new MinorThanEquals();// "<="
                    leq.setLeftExpression(new net.sf.jsqlparser.schema.Column(fieldName));
                    leq.setRightExpression(endRightExp == null ? new StringValue(endTime) : endRightExp);

                    //如果是第一个条件 直接赋值
                    if (ObjectUtil.isNull(queryExpression)) {
                        queryExpression = leq;
                    } else {
                        queryExpression = new AndExpression(queryExpression, leq);
                    }
                }

            } else {

                Object value = MapUtil.get(params, fieldName, Object.class);
                //如果没有数据 则跳过
                if (value == null || StrUtil.isEmpty(String.valueOf(value))) {
                    continue;
                }

                //因为前端传入过来的key  默认都是驼峰命名  但是数据库里面的字段是下划线命名  需要转换一下 再判断是否存在
                Optional<Column> columnOptional = columns.stream().filter(column -> StrUtil.equalsIgnoreCase(column.getName(), fieldName)).findFirst();

                if (columnOptional.isEmpty()) {
                    continue;
                }

                String className = JdbcToJavaUtil.getClassName(columnOptional.get());

                if (LIKE_CLASS_NAME.contains(className)) {

                    LikeExpression likeExpression = new LikeExpression(); // 创建Like表达式对象
                    likeExpression.setLeftExpression(new net.sf.jsqlparser.schema.Column(fieldName));
                    likeExpression.setRightExpression(new StringValue(StringPool.PERCENT + value + StringPool.PERCENT));

                    //如果是第一个条件 直接赋值
                    if (ObjectUtil.isNull(queryExpression)) {
                        queryExpression = likeExpression;
                    } else {
                        queryExpression = new AndExpression(queryExpression, likeExpression);
                    }

                }

                if (EQ_CLASS_NAME.contains(className)) {
                    EqualsTo eq = new EqualsTo();
                    eq.setLeftExpression(new net.sf.jsqlparser.schema.Column(fieldName));
                    eq.setRightExpression(new StringValue(String.valueOf(value)));

                    //如果是第一个条件 直接赋值
                    if (ObjectUtil.isNull(queryExpression)) {
                        queryExpression = eq;
                    } else {
                        queryExpression = new AndExpression(queryExpression, eq);
                    }
                }

                if (TIME_CLASS_NAME.contains(className)) {
                    //根据查询配置的字段名 从参数里面找到对应的值  如果是时间类型 需要查询start 和 end
                    String startTime = MapUtil.get(params, fieldName + FormConstant.START_TIME_SUFFIX, String.class);
                    String endTime = MapUtil.get(params, fieldName + FormConstant.END_TIME_SUFFIX, String.class);

                    if (startTime != null) {
                        GreaterThanEquals geq = new GreaterThanEquals(); // ">="
                        geq.setLeftExpression(new net.sf.jsqlparser.schema.Column(fieldName + FormConstant.START_TIME_SUFFIX));
                        geq.setRightExpression(new StringValue(startTime));

                        //如果是第一个条件 直接赋值
                        if (ObjectUtil.isNull(queryExpression)) {
                            queryExpression = geq;
                        } else {
                            queryExpression = new AndExpression(queryExpression, geq);
                        }
                    }

                    if (endTime != null) {
                        MinorThanEquals leq = new MinorThanEquals();// "<="
                        leq.setLeftExpression(new net.sf.jsqlparser.schema.Column(fieldName + FormConstant.END_TIME_SUFFIX));
                        leq.setRightExpression(new StringValue(endTime));

                        //如果是第一个条件 直接赋值
                        if (ObjectUtil.isNull(queryExpression)) {
                            queryExpression = leq;
                        } else {
                            queryExpression = new AndExpression(queryExpression, leq);
                        }
                    }
                }

            }
        }

        //判断是否存在删除标记字段  如果带有删除标记字段则进行删除标记字段的判断
        if (columns.stream().anyMatch(column -> StrUtil.equalsIgnoreCase(FormConstant.DELETE_MARK, column.getName()))) {
            EqualsTo eq = new EqualsTo();
            eq.setLeftExpression(new net.sf.jsqlparser.schema.Column(FormConstant.DELETE_MARK));
            eq.setRightExpression(new LongValue(DeleteMark.NODELETE.getCode()));

            //如果是第一个条件 直接赋值
            if (ObjectUtil.isNull(queryExpression)) {
                queryExpression = eq;
            } else {
                queryExpression = new AndExpression(queryExpression, eq);
            }
        }

        plainSelect.setWhere(queryExpression);
        Db use = Db.use(datasource);
        use.setRunner(new SqlConnRunner(DialectFactory.getDialect(datasource)));
        PageResult<Entity> pageResult = use.page(plainSelect.toString(), page);

        //将所有查询的数据id 转string
        for (Entity entity : pageResult) {
            entity.set(pkColumn.getName(), entity.get(pkColumn.getName()).toString());
        }

        return new TableDataInfo<>(pageResult, pageResult.getTotal());
    }

    /**
     * 自定义表单-根据配置获取数据-详情
     *
     * @param releaseId 发布 id
     * @param id        自定义表单 id
     */
    @Override
    public Entity query(Long releaseId, Long id) {

        FormRelease formRelease = formReleaseMapper.selectById(releaseId);

        //自定义表单数据
        FormTemplate template = formTemplateMapper.selectById(formRelease.getFormId());

        String formJson = template.getFormJson();
        //自定义表单配置
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formJson, FormDesignConfig.class);
        //表关系配置
        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();
        //主表
        Optional<TableConfig> mainTable = tableConfigs.stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTable.isPresent()) {
            TableConfig tableConfig = mainTable.get();
            String tableName = tableConfig.getTableName();

            Map<String, List<ComponentConfig>> formComponentListMap = GeneratorUtil.buildFormComponentList(formDesignConfig.getFormJson().getList());
            List<String> fieldsList = new ArrayList<>();
            for (ComponentConfig config : formComponentListMap.get(tableName)) {
                // isSave为true不存表
                if (MapUtils.getBoolean(config.getOptions(), "isSave", false)) {
                    continue;
                }
                String type = config.getType();
                if (StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.TIME_RANGE) || StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.DATE_RANGE)) {
                    fieldsList.add(config.getBindStartTime());
                    fieldsList.add(config.getBindEndTime());
                } else {
                    fieldsList.add(config.getBindField());
                }
            }

            return getFormData(tableName, fieldsList, formDesignConfig, id);
        } else {
            throw new ServiceException("主表不存在");
        }
    }

    /**
     * 根据配置信息获取表单数据
     *
     * @param tableName        主表名
     * @param fieldsList       列表所有字段名
     * @param formDesignConfig 表单配置
     * @param id               自定义表单 id
     * @return 列表数据
     */
    @SneakyThrows
    private Entity getFormData(String tableName, List<String> fieldsList, FormDesignConfig formDesignConfig, Long id) {
        //获取表关系配置
        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();
        //获取表结构配置 (因为要根据子表的字段配置 返回需要的数据)
        List<TableStructureConfig> tableStructureConfigs = formDesignConfig.getTableStructureConfigs();

        DataSource datasource = DatasourceUtil.getDataSource(formDesignConfig.getDatabaseId());


        //获取表里所有字段
        Table tableMeta = MetaUtil.getTableMeta(datasource, tableName);
        Collection<Column> columns = tableMeta.getColumns();
        Optional<Column> pk = columns.stream().filter(Column::isPk).findFirst();

        if (pk.isEmpty()) {
            throw new ServiceException("表" + tableName + "没有主键");
        }
        //把主键加入到查询项目
        fieldsList.add(pk.get().getName());
        Entity where = Entity.create(tableName).setFieldNames(fieldsList);

        where.set(pk.get().getName(), id);

        //判断是否存在删除标记字段  如果带有删除标记字段则进行删除标记字段的判断
        if (columns.stream().anyMatch(column -> StrUtil.equalsIgnoreCase(FormConstant.DELETE_MARK, column.getName()))) {
            where.set(FormConstant.DELETE_MARK, DeleteMark.NODELETE.getCode());
        }
        GlobalDbConfig.setCaseInsensitive(false);
        Entity formData = Db.use(datasource).find(where.getFieldNames(), where, new DuoomEntityHandler());

        //遍历所有子表
        List<TableConfig> tableConfigList = tableConfigs.stream().filter(x -> !x.getIsMain()).toList();

        for (TableConfig tableConfig : tableConfigList) {
            Optional<TableStructureConfig> tableStructureConfigOptional = tableStructureConfigs.stream().filter(y -> StrUtil.equalsIgnoreCase(y.getTableName(), tableConfig.getTableName())).findFirst();
            if (tableStructureConfigOptional.isPresent()) {
                TableStructureConfig tableStructureConfig = tableStructureConfigOptional.get();
                List<TableFieldConfig> tableFieldConfigs = tableStructureConfig.getTableFieldConfigs();
                String childTableName = tableStructureConfig.getTableName();
                List<String> childTableFields = tableFieldConfigs.stream().map(TableFieldConfig::getFieldName).collect(Collectors.toList());
                Entity childWhere = Entity.create(childTableName).setFieldNames(childTableFields);

                //获取子表关联主表的关联字段
                Object parentValue = formData.get(tableConfig.getRelationTableField());
                //默认新增条件 子表关联字段 = 主表的关联字段
                childWhere.set(tableConfig.getRelationField(), parentValue);

                //判断是否存在删除标记字段  如果带有删除标记字段则进行删除标记字段的判断
                Table childTableMeta = MetaUtil.getTableMeta(datasource, childTableName);
                Collection<Column> childColumns = childTableMeta.getColumns();

                if (childColumns.stream().anyMatch(column -> StrUtil.equalsIgnoreCase(FormConstant.DELETE_MARK, column.getName()))) {
                    childWhere.set(FormConstant.DELETE_MARK, DeleteMark.NODELETE.getCode());
                }

                List<Entity> childList = Db.use(datasource).find(childWhere, new DuoomEntityListHandler());

                formData.set(childTableName + "List", childList);
            }
        }

        return formData;
    }

    /**
     * 自定义表单-新增
     */
    @Override
    public Boolean add(FormExecuteBo bo) {

        FormRelease formRelease = formReleaseMapper.selectById(bo.getReleaseId());

        //自定义表单数据
        FormTemplate template = formTemplateMapper.selectById(formRelease.getFormId());

        return insertFormData(bo.getFormData(), template);
    }

    private boolean insertFormData(Map<String, Object> formData, FormTemplate template) {
        String formJson = template.getFormJson();
        //自定义表单配置
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formJson, FormDesignConfig.class);
        //表关系配置
        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();
        //主表
        Optional<TableConfig> mainTable = tableConfigs.stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTable.isPresent()) {
            String tableName = mainTable.get().getTableName();

            Entity where = Entity.create(tableName);

            DataSource datasource = DatasourceUtil.getDataSource(formDesignConfig.getDatabaseId());
            //获取表里所有字段
            Table tableMeta = MetaUtil.getTableMeta(datasource, tableName);
            Collection<Column> columns = tableMeta.getColumns();
            Optional<Column> pk = columns.stream().filter(Column::isPk).findFirst();

            long keyValue = IdUtil.getSnowflakeNextId();

            //遍历所有子表
            List<TableConfig> tableConfigList = tableConfigs.stream().filter(x -> !x.getIsMain()).collect(Collectors.toList());

            //所有子表数据
            Map<String, List<Entity>> childMaps = new HashMap<>(tableConfigList.size());
            // 自动编码code
            List<String> autoCodeList = new ArrayList<>();
            // 处理字段值
            Map<String, Object> toSaveFormData = handleFormDataForSave(formData, formDesignConfig, tableName, autoCodeList);

            //formData 默认插入雪花Id主键
            if (pk.isPresent()) {
                formData.put(pk.get().getName(), keyValue);
                toSaveFormData.put(pk.get().getName(), keyValue);
            }

            //构建子表单数据
            wrapperChildEntity(datasource, tableConfigList, childMaps, formData, formDesignConfig, autoCodeList);


            //此时的formData 已经是剔除了子表单数据了
            where.putAll(toSaveFormData);
            //如果有审计字段  默认填充值
            putAuditEntityInsertData(where, columns);

            Session session = Session.create(datasource);
            try {
                session.beginTransaction();
                // 保存主表数据
                session.insert(where);
                // 保存子表数据
                for (Map.Entry<String, List<Entity>> tableMap : childMaps.entrySet()) {
                    List<Entity> childList = tableMap.getValue();
                    session.insert(childList);
                }
                codeRuleService.useEncode(autoCodeList);
                session.commit();
            } catch (SQLException e) {
                session.quietRollback();
                log.error("新增数据失败,数据回滚!", e);
                throw new ServiceException("新增数据失败");
            }
            return true;
        } else {
            throw new ServiceException("主表不存在");
        }
    }

    /**
     * 处理保存的表单数据
     */
    private Map<String, Object> handleFormDataForSave(Map<String, Object> formData, FormDesignConfig formDesignConfig,
                                                      String tableName, List<String> autoCodeList) {
        Map<String, Object> resultData = new HashMap<>(formData.size());
        Map<String, List<ComponentConfig>> componentListMap = GeneratorUtil.buildFormComponentList(formDesignConfig.getFormJson().getList());
        List<ComponentConfig> configList = componentListMap.get(tableName);
        Set<String> fieldNameList = formData.keySet();
        DbType dbType = DatasourceUtil.getDbType(formDesignConfig.getDatabaseId());
        for (ComponentConfig config : configList) {
            Map<String, Object> options = config.getOptions();
            if (org.apache.commons.collections.MapUtils.getBoolean(options, "isSave", false)) {
                continue;
            }
            String bindField = config.getBindField();
            String bindStartTimeField = config.getBindStartTime();
            String bindEndTimeField = config.getBindEndTime();
            boolean isMatch = false;
            for (String fieldName : fieldNameList) {
                if (StrUtil.equalsIgnoreCase(fieldName, bindField)
                    || StrUtil.equalsIgnoreCase(fieldName, bindStartTimeField)
                    || StrUtil.equalsIgnoreCase(fieldName, bindEndTimeField)) {
                    resultData.put(fieldName, formData.get(fieldName));
                    isMatch = true;
                }
            }
            if (!isMatch) continue;
            String type = config.getType();
            Integer infoType = org.apache.commons.collections.MapUtils.getInteger(options, "infoType");
            String format = org.apache.commons.collections.MapUtils.getString(options, "format", LocalDateTimeUtil.LOCAL_DATE_TIME_FORMAT);
            if (StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.TIME)) {
                Object timeObj = org.apache.commons.collections.MapUtils.getObject(formData, bindField);
                if (timeObj instanceof String) {
                    resultData.put(bindField, LocalDateTimeUtil.parseDbTime(String.valueOf(timeObj), dbType));
                } else if (timeObj instanceof LocalTime) {
                    resultData.put(bindField, timeObj);
                } else {
                    resultData.put(bindField, null);
                }
            } else if (StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.TIME_RANGE)) {
                Object start = org.apache.commons.collections.MapUtils.getObject(formData, bindStartTimeField);
                Object end = org.apache.commons.collections.MapUtils.getObject(formData, bindEndTimeField);
                if (ObjectUtil.isNotEmpty(start)) {
                    if (start instanceof String) {
                        resultData.put(bindStartTimeField, LocalDateTimeUtil.parseDbTime(String.valueOf(start), dbType));
                    } else {
                        resultData.put(bindStartTimeField, start);
                    }
                } else {
                    resultData.put(bindStartTimeField, null);
                }
                if (ObjectUtil.isNotEmpty(end)) {
                    if (end instanceof String) {
                        resultData.put(bindEndTimeField, LocalDateTimeUtil.parseDbTime(String.valueOf(end), dbType));
                    } else {
                        resultData.put(bindEndTimeField, end);
                    }
                } else {
                    resultData.put(bindEndTimeField, null);
                }
            } else if (StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.DATE)
                || (StrUtil.equalsIgnoreCase(ComponentTypeConstant.INFO, type) && infoType.equals(2))) {
                Object valueStr = org.apache.commons.collections.MapUtils.getObject(formData, bindField);
                if (ObjectUtil.isNotEmpty(valueStr)) {

                    resultData.put(bindField, valueStr instanceof LocalDateTime ? valueStr : DateUtil.parseDate(Objects.requireNonNull(LocalDateTimeUtil.parseDate(String.valueOf(valueStr), format)).toString()));
                } else {
                    resultData.put(bindField, null);
                }
            } else if (StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.DATE_RANGE)) {
                Object start = org.apache.commons.collections.MapUtils.getObject(formData, bindStartTimeField);
                Object end = org.apache.commons.collections.MapUtils.getObject(formData, bindEndTimeField);
                if (ObjectUtil.isNotEmpty(start)) {
                    resultData.put(bindStartTimeField, start instanceof LocalDateTime ? start : DateUtil.parseDate(Objects.requireNonNull(LocalDateTimeUtil.parseDate(String.valueOf(start), format)).toString()));
                } else {
                    resultData.put(bindStartTimeField, null);
                }
                if (ObjectUtil.isNotEmpty(end)) {
                    resultData.put(bindEndTimeField, end instanceof LocalDateTime ? end : DateUtil.parseDate(Objects.requireNonNull(LocalDateTimeUtil.parseDate(String.valueOf(end), format)).toString()));
                } else {
                    resultData.put(bindEndTimeField, null);
                }
            } else if (StrUtil.equalsIgnoreCase(type, ComponentTypeConstant.AUTO_CODE)) {
                if (autoCodeList != null)
                    autoCodeList.add(org.apache.commons.collections.MapUtils.getString(options, ComponentTypeConstant.AUTO_CODE_RULE));
            }
        }
        return resultData;
    }

    /**
     * 新增数据时候 构建子表单Entity
     *
     */
    private Map<String, List<Map<String, Object>>> wrapperChildEntity(DataSource datasource, List<TableConfig> tableConfigList,
                                                                      Map<String, List<Entity>> childMaps, Map<String, Object> formData,
                                                                      FormDesignConfig formDesignConfig, List<String> autoCodeList) {

        Map<String, List<Map<String, Object>>> childFormData = new HashMap<>();

        for (TableConfig tableConfig : tableConfigList) {

            //获取表里所有字段
            Table childTableMeta = MetaUtil.getTableMeta(datasource, tableConfig.getTableName());
            Collection<Column> childColumns = childTableMeta.getColumns();

            Optional<Column> childPkOptional = childColumns.stream().filter(Column::isPk).findFirst();

            List<Map<String, Object>> childMap = new ArrayList<>();
            List<Map<String, Object>> resultMap = new ArrayList<>();

            Class<List<Map<String, Object>>> childMapClass = ClassUtil.getClass(childMap);
            //获取子表的数据
            childMap = MapUtil.get(formData, tableConfig.getTableName() + "List", childMapClass);

            List<Entity> childEntities = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(childMap)) {
                for (Map<String, Object> stringObjectMap : childMap) {
                    Map<String, Object> handedMap = handleFormDataForSave(stringObjectMap, formDesignConfig, tableConfig.getTableName(), autoCodeList);
                    Entity entity = Entity.create(tableConfig.getTableName());
                    entity.putAll(handedMap);

                    //获取子表关联主表的关联字段
                    Object parentValue = formData.get(tableConfig.getRelationTableField());
                    //默认新增条件 子表关联字段 = 主表的关联字段
                    entity.set(tableConfig.getRelationField(), parentValue);

                    //填充子表主键
                    childPkOptional.ifPresent(pk -> {
                        long snowflakeNextId = IdUtil.getSnowflakeNextId();
                        entity.put(pk.getName(), snowflakeNextId);
                        handedMap.put(pk.getName(), snowflakeNextId);
                        formData.get(tableConfig.getRelationTableField());
                        if (ObjectUtil.isNotEmpty(parentValue)) {
                            //将子表的parent_id,赋值为主表的主键值
                            entity.put(tableConfig.getRelationField(), parentValue);
                            handedMap.put(tableConfig.getRelationField(), parentValue);
                        }
                    });
                    resultMap.add(handedMap);


                    //如果有审计字段  默认填充值
                    putAuditEntityInsertData(entity, childColumns);

                    childEntities.add(entity);
                }
            }

            //获取到子表的所有数据
            childMaps.put(tableConfig.getTableName(), childEntities);
            childFormData.put(tableConfig.getTableName(), resultMap);

            //移除Map中 子表的数据  避免sql错误；
            MapUtil.removeAny(formData, tableConfig.getTableName() + "List");
        }

        return childFormData;
    }

    /**
     * 填充审计字段 新增
     */
    private void putAuditEntityInsertData(Entity entity, Collection<Column> columns) {
        for (Column column : columns) {
            if (StrUtil.equalsIgnoreCase(FormConstant.CREATE_BY, column.getName())) {
                entity.set(FormConstant.CREATE_BY, StpUtil.getLoginIdAsLong());
            }
            if (StrUtil.equalsIgnoreCase(FormConstant.CREATE_TIME, column.getName())) {
                entity.set(FormConstant.CREATE_TIME, Timestamp.valueOf(LocalDateTime.now()));
            }
            if (StrUtil.equalsIgnoreCase(FormConstant.DELETE_MARK, column.getName())) {
                entity.set(FormConstant.DELETE_MARK, DeleteMark.NODELETE.getCode());
            }
            if (StrUtil.equalsIgnoreCase(FormConstant.STATUS, column.getName())) {
                entity.set(FormConstant.STATUS, StatusEnum.YES.getCode());
            }
            if (StrUtil.equalsIgnoreCase(FormConstant.AUTH_USER_ID, column.getName())) {
                entity.set(FormConstant.AUTH_USER_ID, StpUtil.getLoginIdAsLong());
            }
        }
    }

    /**
     * 自定义表单-修改
     */
    @Override
    public Boolean update(FormExecuteBo bo) {
        FormRelease formRelease = formReleaseMapper.selectById(bo.getReleaseId());

        Map<String, Object> formData = bo.getFormData();

        //自定义表单数据
        FormTemplate template = formTemplateMapper.selectById(formRelease.getFormId());

        return updateFormData(formData, template);
    }

    private boolean updateFormData(Map<String, Object> formData, FormTemplate template) {
        String formJson = template.getFormJson();
        //自定义表单配置
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formJson, FormDesignConfig.class);
        //表关系配置
        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();
        //主表
        Optional<TableConfig> mainTable = tableConfigs.stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTable.isPresent()) {
            String tableName = mainTable.get().getTableName();

            Entity entity = Entity.create(tableName);

            DataSource datasource = DatasourceUtil.getDataSource(formDesignConfig.getDatabaseId());
            //获取表里所有字段
            Table tableMeta = MetaUtil.getTableMeta(datasource, tableName);
            Collection<Column> columns = tableMeta.getColumns();
            Optional<Column> pkOptional = columns.stream().filter(Column::isPk).findFirst();
            Column pk;
            if (pkOptional.isPresent()) {
                pk = pkOptional.get();
            } else {
                throw new ServiceException("主键不存在");
            }


            //遍历所有子表
            List<TableConfig> tableConfigList = tableConfigs.stream().filter(x -> !x.getIsMain()).collect(Collectors.toList());

            //所有子表数据
            Map<String, List<Entity>> childMaps = new HashMap<>(tableConfigList.size());

            //更新的时候默认使用string 的key  因为客户的表 可能什么类型的主键都有  所以默认使用string
            String keyValue = MapUtil.get(formData, pk.getName(), String.class);


            Object keyValue2 = null;
            if (StrUtil.equalsIgnoreCase(JdbcToJavaUtil.getClassName(pk), "Long")) {
                keyValue2 = Long.valueOf(keyValue);
            } else {
                keyValue2 = keyValue;
            }
            formData.put(pk.getName(), keyValue2);
            //where 拼接 id
            Entity where = Entity.create(tableName).set(pk.getName(), keyValue2);

            //构建子表单数据
            wrapperChildEntity(datasource, tableConfigList, childMaps, formData, formDesignConfig, null);

            // 处理字段值
            formData = handleFormDataForSave(formData, formDesignConfig, tableName, null);

            //此时的formData 已经是剔除了子表单数据了
            entity.putAll(formData);

            //主表如果有审计字段  默认填充值
            putAuditEntityUpdateData(entity, columns);

            Session session = Session.create(datasource);
            try {
                session.beginTransaction();
                // 更新主表数据
                session.update(entity, where);


                // 遍历数据 根据 表名 保存子表数据
                for (Map.Entry<String, List<Entity>> tableMap : childMaps.entrySet()) {
                    //先删除子表单数据 然后再新增  这里使用物理删除  不再逻辑删除。  tableMap的key  就是 子表的表名
                    Optional<TableConfig> childTableConfig = tableConfigList.stream().filter(x -> x.getTableName().equals(tableMap.getKey())).findFirst();

                    if (childTableConfig.isPresent()) {
                        //删除子表单数据
                        Entity childDeleteWhere = Entity.create(tableMap.getKey()).set(childTableConfig.get().getRelationField(), formData.get(childTableConfig.get().getRelationTableField()));
                        session.del(childDeleteWhere);

                        //再重新新增
                        List<Entity> childList = tableMap.getValue();
                        session.insert(childList);
                    }

                }
                session.commit();
            } catch (SQLException e) {
                session.quietRollback();
                log.error("修改数据失败，数据回滚！", e);
                throw new ServiceException("修改数据失败");
            }

            return true;
        } else {
            throw new ServiceException("主表不存在");
        }
    }

    /**
     * 填充审计字段 修改
     */
    private void putAuditEntityUpdateData(Entity entity, Collection<Column> columns) {
        for (Column column : columns) {
            if (StrUtil.equalsIgnoreCase(FormConstant.UPDATE_BY, column.getName())) {
                entity.set(FormConstant.UPDATE_BY, LoginHelper.getUserId());
            }
            if (StrUtil.equalsIgnoreCase(FormConstant.UPDATE_TIME, column.getName())) {
                entity.set(FormConstant.UPDATE_TIME, Timestamp.valueOf(LocalDateTime.now()));
            }
        }
    }

    /**
     * 自定义表单-删除
     */
    @Override
    public Boolean remove(FormExecuteDeleteBo bo) {

        FormRelease formRelease = formReleaseMapper.selectById(bo.getReleaseId());

        String configJson = formRelease.getConfigJson();

        //自定义表单数据
        FormTemplate template = formTemplateMapper.selectById(formRelease.getFormId());

        String formJson = template.getFormJson();
        //自定义表单配置
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formJson, FormDesignConfig.class);
        //表关系配置
        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();
        //主表
        Optional<TableConfig> mainTable = tableConfigs.stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTable.isPresent()) {
            String tableName = mainTable.get().getTableName();

            Entity entity = Entity.create(tableName);

            DataSource datasource = DatasourceUtil.getDataSource(formDesignConfig.getDatabaseId());
            //获取表里所有字段
            Table tableMeta = MetaUtil.getTableMeta(datasource, tableName);
            Collection<Column> columns = tableMeta.getColumns();
            Optional<Column> pkOptinal = columns.stream().filter(Column::isPk).findFirst();
            Column pk;
            if (pkOptinal.isPresent()) {
                pk = pkOptinal.get();
            } else {
                throw new ServiceException("主键不存在");
            }


            //获取所有需要删除的id
            List<String> keyValues = bo.getIds();

            //获取到所有子表
            List<TableConfig> tableConfigList = tableConfigs.stream().filter(x -> !x.getIsMain()).toList();

            Entity where = Entity.create(tableName);

            if (StrUtil.equalsIgnoreCase(JdbcToJavaUtil.getClassName(pk), "Long")) {
                where.set(pk.getName(), keyValues.stream().map(Long::parseLong).collect(Collectors.toList()));
            } else {
                where.set(pk.getName(), keyValues);
            }

            //如果有审计字段  默认填充值
            putAuditEntityUpdateData(entity, columns);

            //如果包含逻辑删除字段
            if (columns.stream().anyMatch(x -> StrUtil.equalsIgnoreCase(FormConstant.DELETE_MARK, x.getName()))) {

                Session session = Session.create(datasource);
                try {
                    session.beginTransaction();

                    for (TableConfig tableConfig : tableConfigList) {

                        Optional<TableConfig> childTableConfig = tableConfigList.stream().filter(x -> x.getTableName().equals(tableConfig.getTableName())).findFirst();

                        if (childTableConfig.isPresent()) {

                            List<Entity> entities = session.find(where);

                            List<Object> allPkValue = entities.stream().map(x -> x.get(childTableConfig.get().getRelationTableField())).collect(Collectors.toList());

                            //删除子表单数据
                            Entity childDeleteWhere = Entity.create(tableConfig.getTableName()).
                                set(childTableConfig.get().getRelationField(), allPkValue);

                            //没做子表单的软删除
                            session.del(childDeleteWhere);

                        }
                    }

                    // 更新主表数据
                    entity.set(FormConstant.DELETE_MARK, DeleteMark.DELETED.getCode());
                    session.update(entity, where);


                    session.commit();
                } catch (SQLException e) {
                    session.quietRollback();
                    throw new ServiceException("删除数据失败，数据回滚！");
                }
            } else {

                Session session = Session.create(datasource);
                try {
                    session.beginTransaction();

                    //删除子表数据
                    for (TableConfig tableConfig : tableConfigList) {

                        Optional<TableConfig> childTableConfig = tableConfigList.stream().filter(x -> x.getTableName().equals(tableConfig.getTableName())).findFirst();

                        if (childTableConfig.isPresent()) {

                            List<Entity> entities = session.find(where);

                            List<Object> allPkValue = entities.stream().map(x -> x.get(childTableConfig.get().getRelationTableField())).collect(Collectors.toList());

                            //删除子表单数据
                            Entity childDeleteWhere = Entity.create(tableConfig.getTableName()).
                                set(childTableConfig.get().getRelationField(), allPkValue);

                            //没做子表单的软删除
                            session.del(childDeleteWhere);

                        }
                    }

                    //删除主表数据
                    session.del(where);


                    session.commit();
                } catch (SQLException e) {
                    session.quietRollback();
                    log.error("删除数据失败，数据回滚！", e);
                    throw new ServiceException("删除数据失败");
                }
            }
            return true;
        } else {
            throw new ServiceException("主表不存在");
        }
    }

    /**
     * 自定义表单-工作流模块-根据配置获取表单数据
     */
    @Override
    public Entity workFlowInfo(FormExecuteWorkflowInfoBo bo) {
        //自定义表单数据
        FormTemplate template = formTemplateMapper.selectById(bo.getFormId());

        String formJson = template.getFormJson();
        //自定义表单配置
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formJson, FormDesignConfig.class);
        //表关系配置
        List<TableConfig> tableConfigs = formDesignConfig.getTableConfigs();
        //主表
        Optional<TableConfig> mainTable = tableConfigs.stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTable.isPresent()) {
            TableConfig tableConfig = mainTable.get();
            String tableName = tableConfig.getTableName();

            Optional<TableStructureConfig> mainConfig = formDesignConfig.getTableStructureConfigs().stream().filter(x->x.getTableName().equals(tableName)).findFirst();

            if (mainConfig.isEmpty()) {
                throw new ServiceException("主表不存在");
            }
            List<String> fieldsList = mainConfig.get().getTableFieldConfigs().stream().map(TableFieldConfig::getFieldName).collect(Collectors.toList());

            return getFormData(tableName, fieldsList, formDesignConfig, bo.getId());
        } else {
            throw new ServiceException("主表不存在");
        }
    }
}
