package com.duoom.forms.domain.config;

import lombok.Data;

import java.util.Map;

/**
 * 表单组件属性配置
 * @Author: duoom
 * @Date: 2022/5/11 15:45
 */
@Data
public class FormAttrConfig {
    /**
     * 表单类型，modal,drawer
     */
    private String formType;
    /**
     * 表单大小 根据antd表单属性来 large  medium  small default
     */
    private String size;

    /**
     * 是否隐藏表单必填标记
     */
    private Boolean hideRequiredMark;

    /**
     * 表单布局方式 根据antd表单布局来 horizontal  vertical  inline
     */
    private String layout;

    /**
     * 表单标签位置 根据antd 属性来 left  right
     */
    private String labelAlign;

    /**
     * 表单的样式（标题宽度）
     */
    private Map<String, Object> labelCol;

    /**
     * 弹窗宽度
     */
    private Integer formWidth;
}
