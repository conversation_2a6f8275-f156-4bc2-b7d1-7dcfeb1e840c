package com.duoom.forms.domain.vo;

import com.duoom.forms.domain.CodeRuleSeed;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 编号规则种子表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-23
 */
@Data
@AutoMapper(target = CodeRuleSeed.class)
public class CodeRuleSeedVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编码规则主键
     */
    private Long ruleId;

    /**
     * 用户主键
     */
    private Long userId;

    /**
     * 种子值
     */
    private Integer seedValue;

    /**
     * 状态【Y：正常；N：关闭】
     */
    private String status;


}
