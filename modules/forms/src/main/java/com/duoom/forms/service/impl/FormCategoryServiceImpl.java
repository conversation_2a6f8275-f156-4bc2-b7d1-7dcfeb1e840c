package com.duoom.forms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.duoom.common.core.constant.Constants;
import com.duoom.common.core.service.FormCategoryService;
import com.duoom.forms.constant.FormConstant;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.core.utils.MapstructUtils;
import com.duoom.common.core.utils.ObjectUtils;
import com.duoom.common.core.utils.StringUtils;
import com.duoom.common.core.utils.TreeBuildUtils;
import com.duoom.common.mybatis.core.query.LambdaQueryWrapperX;
import com.duoom.common.mybatis.helper.DataBaseHelper;
import com.duoom.common.satoken.utils.LoginHelper;
import com.duoom.forms.domain.FormCategory;
import com.duoom.forms.domain.bo.FormCategoryBo;
import com.duoom.forms.domain.vo.FormCategoryVo;
import com.duoom.forms.mapper.FormCategoryMapper;
import com.duoom.forms.service.IFormCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 表单分类Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class FormCategoryServiceImpl implements IFormCategoryService, FormCategoryService {

    private final FormCategoryMapper baseMapper;

    /**
     * 查询表单分类
     *
     * @param categoryId 主键
     * @return 表单分类
     */
    @Override
    public FormCategoryVo queryById(Long categoryId) {
        FormCategoryVo category = baseMapper.selectVoById(categoryId);
        if (ObjectUtil.isNull(category)) {
            return null;
        }
        FormCategoryVo parentCategory = baseMapper.selectVoOne(new LambdaQueryWrapper<FormCategory>()
            .select(FormCategory::getCategoryName).eq(FormCategory::getCategoryId, category.getParentId()));
        category.setParentName(ObjectUtils.notNullGetter(parentCategory, FormCategoryVo::getCategoryName));
        return category;
    }

    /**
     * 根据表单分类ID查询表单分类名称
     *
     * @param categoryId 表单分类ID
     * @return 表单分类名称
     */
    @Cacheable(cacheNames = FormConstant.FORM_CATEGORY_NAME, key = "#categoryId")
    @Override
    public String selectCategoryNameById(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            return null;
        }
        FormCategory category = baseMapper.selectOne(new LambdaQueryWrapper<FormCategory>()
            .select(FormCategory::getCategoryName).eq(FormCategory::getCategoryId, categoryId));
        return ObjectUtils.notNullGetter(category, FormCategory::getCategoryName);
    }

    /**
     * 查询符合条件的表单分类列表
     *
     * @param bo 查询条件
     * @return 表单分类列表
     */
    @Override
    public List<FormCategoryVo> queryList(FormCategoryBo bo) {
        LambdaQueryWrapper<FormCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询表单分类树结构信息
     *
     * @param category 表单分类信息
     * @return 表单分类树信息集合
     */
    @Override
    public List<Tree<String>> selectCategoryTreeList(FormCategoryBo category) {
        LambdaQueryWrapper<FormCategory> lqw = buildQueryWrapper(category);
        List<FormCategoryVo> categoryList = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(categoryList)) {
            return CollUtil.newArrayList();
        }
        return TreeBuildUtils.build(
            categoryList,
            item -> String.valueOf(item.getCategoryId()),
            item -> String.valueOf(item.getParentId()),
            (item, tree) -> tree
                .setName(item.getCategoryName())
                .setWeight(item.getOrderNum())
        );
    }

    /**
     * 校验表单分类是否有数据权限
     *
     * @param categoryId 表单分类ID
     */
    @Override
    public void checkCategoryDataScope(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        if (baseMapper.countCategoryById(categoryId) == 0) {
            throw new ServiceException("没有权限访问表单分类数据！");
        }
    }

    /**
     * 校验表单分类名称是否唯一
     *
     * @param category 表单分类信息
     * @return 结果
     */
    @Override
    public boolean checkCategoryNameUnique(FormCategoryBo category) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<FormCategory>()
            .eq(FormCategory::getCategoryName, category.getCategoryName())
            .eq(FormCategory::getParentId, category.getParentId())
            .ne(ObjectUtil.isNotNull(category.getCategoryId()), FormCategory::getCategoryId, category.getCategoryId()));
        return !exist;
    }

    /**
     * 查询表单分类是否存在流程定义
     *
     * @param categoryId 表单分类ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkCategoryExistDefinition(Long categoryId) {
        // TODO  待完善
        return true;
    }

    /**
     * 是否存在表单分类子节点
     *
     * @param categoryId 表单分类ID
     * @return 结果
     */
    @Override
    public boolean hasChildByCategoryId(Long categoryId) {
        return baseMapper.exists(new LambdaQueryWrapper<FormCategory>()
            .eq(FormCategory::getParentId, categoryId));
    }

    private LambdaQueryWrapper<FormCategory> buildQueryWrapper(FormCategoryBo bo) {
        LambdaQueryWrapperX<FormCategory> lqw = new LambdaQueryWrapperX<FormCategory>();
        lqw.eq(FormCategory::getDelFlag, Constants.SUCCESS);
        lqw.eqIf(FormCategory::getCategoryId, bo.getCategoryId());
        lqw.eqIf(FormCategory::getParentId, bo.getParentId());
        lqw.likeIf(FormCategory::getCategoryName, bo.getCategoryName());
        lqw.orderByAsc(FormCategory::getAncestors);
        lqw.orderByAsc(FormCategory::getParentId);
        lqw.orderByAsc(FormCategory::getOrderNum);
        lqw.orderByAsc(FormCategory::getCategoryId);
        return lqw;
    }

    /**
     * 新增表单分类
     *
     * @param bo 表单分类
     * @return 是否新增成功
     */
    @Override
    public int insertByBo(FormCategoryBo bo) {
        FormCategory info = baseMapper.selectById(bo.getParentId());
        FormCategory category = MapstructUtils.convert(bo, FormCategory.class);
        category.setAncestors(info.getAncestors() + StringUtils.SEPARATOR + category.getParentId());
        return baseMapper.insert(category);
    }

    /**
     * 修改表单分类
     *
     * @param bo 表单分类
     * @return 是否修改成功
     */
    @CacheEvict(cacheNames = FormConstant.FORM_CATEGORY_NAME, key = "#bo.categoryId")
    @Override
    public int updateByBo(FormCategoryBo bo) {
        FormCategory category = MapstructUtils.convert(bo, FormCategory.class);
        FormCategory oldCategory = baseMapper.selectById(category.getCategoryId());
        if (ObjectUtil.isNull(oldCategory)) {
            throw new ServiceException("表单分类不存在，无法修改");
        }
        if (!oldCategory.getParentId().equals(category.getParentId())) {
            // 如果是新父表单分类 则校验是否具有新父表单分类权限 避免越权
            this.checkCategoryDataScope(category.getParentId());
            FormCategory newParentCategory = baseMapper.selectById(category.getParentId());
            if (ObjectUtil.isNotNull(newParentCategory)) {
                String newAncestors = newParentCategory.getAncestors() + StringUtils.SEPARATOR + newParentCategory.getCategoryId();
                String oldAncestors = oldCategory.getAncestors();
                category.setAncestors(newAncestors);
                updateCategoryChildren(category.getCategoryId(), newAncestors, oldAncestors);
            }
        } else {
            category.setAncestors(oldCategory.getAncestors());
        }
        return baseMapper.updateById(category);
    }

    /**
     * 修改子元素关系
     *
     * @param categoryId   被修改的表单分类ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    private void updateCategoryChildren(Long categoryId, String newAncestors, String oldAncestors) {
        List<FormCategory> children = baseMapper.selectList(new LambdaQueryWrapper<FormCategory>()
            .apply(DataBaseHelper.findInSet(categoryId, "ancestors")));
        List<FormCategory> list = new ArrayList<>();
        for (FormCategory child : children) {
            FormCategory category = new FormCategory();
            category.setCategoryId(child.getCategoryId());
            category.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
            list.add(category);
        }
        if (CollUtil.isNotEmpty(list)) {
            baseMapper.updateBatchById(list);
        }
    }

    /**
     * 删除表单分类信息
     *
     * @param categoryId 主键
     * @return 是否删除成功
     */
    @CacheEvict(cacheNames = FormConstant.FORM_CATEGORY_NAME, key = "#categoryId")
    @Override
    public int deleteWithValidById(Long categoryId) {
        return baseMapper.deleteById(categoryId);
    }



    /**
     * 根据消息模板 id 查模板名称
     *
     * @param id 模板 Id
     * @return 模板名称
     */
    @Override
    public String selectFormCategoryNameById(Long id) {
        FormCategory formCategory = baseMapper.selectById(id);
        return ObjectUtils.notNullGetter(formCategory, FormCategory::getCategoryName);
    }
}
