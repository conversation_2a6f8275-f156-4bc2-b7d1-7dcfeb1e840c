package com.duoom.forms.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.duoom.common.core.domain.R;
import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.idempotent.annotation.RepeatSubmit;
import com.duoom.common.log.annotation.Log;
import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.common.web.core.BaseController;
import com.duoom.forms.domain.bo.FormReleaseBo;
import com.duoom.forms.domain.vo.FormReleaseVo;
import com.duoom.forms.service.IFormReleaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 自定义表单 发布表
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/form/release")
public class FormReleaseController extends BaseController {

    private final IFormReleaseService iFormReleaseService;

    /**
     * 表单发布-列表
     */
    @GetMapping(value = "/list")
    @Log(title = "表单发布", businessType = BusinessType.SELECT)
    @SaCheckPermission("form:release:list")
    public TableDataInfo<FormReleaseVo> list(PageQuery pageQuery) {
        return iFormReleaseService.list(pageQuery);
    }

    /**
     * 表单发布-新增（发布）
     */
    @PostMapping
    @Log(title = "表单发布", businessType = BusinessType.INSERT)
    @SaCheckPermission("form:release:add")
    @RepeatSubmit
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FormReleaseBo bo) {
        return toAjax(iFormReleaseService.add(bo));
    }

    /**
     * 表单发布-修改
     */
    @PutMapping("/update")
    @Log(title = "表单发布", businessType = BusinessType.UPDATE)
    @SaCheckPermission("form:release:edit")
    @RepeatSubmit
    public R<Void> update(@Validated(EditGroup.class) @RequestBody FormReleaseBo bo) {
        return toAjax(iFormReleaseService.update(bo));
    }

    /**
     * 表单发布-删除
     * @param id 表单发布 Id
     */
    @DeleteMapping("/{id}")
    @Log(title = "表单发布", businessType = BusinessType.DELETE)
    @SaCheckPermission("form:release:remove")
    @RepeatSubmit
    public R<Void> remove(@PathVariable Long id){
        return toAjax(iFormReleaseService.remove(id));
    }

    /**
     * 表单发布-详情
     * @param id 表单发布 Id
     */
    @GetMapping("/{id}")
    @Log(title = "表单发布", businessType = BusinessType.SELECT)
    @SaCheckPermission("form:release:query")
    public R<FormReleaseVo> query(@PathVariable Long id){
        return R.ok(iFormReleaseService.query(id));
    }

}
