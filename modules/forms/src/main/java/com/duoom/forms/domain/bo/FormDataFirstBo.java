package com.duoom.forms.domain.bo;

import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.forms.domain.config.FormDesignConfig;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 自定义表单设计数据优先
 * @Author: duoom
 * @Date: 2022/5/9 16:37
 */
@Data
public class FormDataFirstBo {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     *表单模板名称
     */
    @NotBlank(message = "表单模板名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     *表单分类 关联 表单分类 id
     */
    private Long category;

    /**
     *表单类型:0 系统表单 1 自定义表单
     */
    @Size(min = 0, max = 1, message = "表单类型长度不能超过{max}个字符")
    private Integer formType = 1;

    /**
     *表单设计类型（0-数据优先，1-界面优先，2-简易模板）
     */
    private Integer formDesignType;

    /**
     *备注
     */
    private String remark;

    /**
     * 表单设计json
     */
    @NotNull(message = "表单设计json不能为空", groups = {AddGroup.class, EditGroup.class})
    private FormDesignConfig formJson;


    /**
     * 是否需要修改表（修改界面优先或简易模板可传，其他接口不传）
     */
    private Boolean isChange = false;

    /**
     * 是否创建新表（修改界面优先或简易模板可传，其他接口不传）
     */
    private Boolean isCreateNewTable = false;


}
