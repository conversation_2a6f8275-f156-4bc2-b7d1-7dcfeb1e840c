package com.duoom.forms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 数据权限 对象类型关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@TableName("data_auth_relation")
@Data
public class DataAuthRelation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * data_auth 表id
     */
    private Long dataAuthId;

    /**
     * 对象id  如果data_auth 表中的类型是 角色  就是角色id  如果是用户 就是用户id
     */
    private Long objectId;


}
