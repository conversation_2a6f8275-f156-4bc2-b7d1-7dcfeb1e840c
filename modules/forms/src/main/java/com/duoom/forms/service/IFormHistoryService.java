package com.duoom.forms.service;

import com.duoom.forms.domain.vo.FormHistoryVo;

import java.util.List;

/**
 * <p>
 * 自定义表单历史表  每次修改自定义表单会新增一条数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface IFormHistoryService {

    /**
     * 表单历史-列表
     *
     * @param formId 表单 id
     */
    List<FormHistoryVo> list(Long formId);

    /**
     * 表单历史-更新到此版本
     * @param formId 模板 id
     * @param id 历史记录 id
     */
    Boolean change(Long formId, Long id);

    /**
     * 表单历史-设计模板详情
     */
    String query(Long id);
}
