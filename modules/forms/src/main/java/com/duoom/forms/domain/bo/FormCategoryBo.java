package com.duoom.forms.domain.bo;

import com.duoom.common.core.validate.AddGroup;
import com.duoom.common.core.validate.EditGroup;
import com.duoom.common.mybatis.core.domain.BaseEntity;
import com.duoom.forms.domain.FormCategory;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单分类业务对象 wf_category
 *
 * <AUTHOR>
 * @date 2023-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FormCategory.class, reverseConvertGenerate = false)
public class FormCategoryBo extends BaseEntity {

    /**
     * 表单分类ID
     */
    @NotNull(message = "表单分类ID不能为空", groups = { EditGroup.class })
    private Long categoryId;

    /**
     * 父表单分类id
     */
    @NotNull(message = "父表单分类id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long parentId;

    /**
     * 表单分类名称
     */
    @NotBlank(message = "表单分类名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryName;

    /**
     * 显示顺序
     */
    private Long orderNum;

}
