package com.duoom.forms.domain.vo;

import com.duoom.common.translation.annotation.Translation;
import com.duoom.common.translation.constant.TransConstant;
import com.duoom.forms.domain.FormTemplate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @Author: duoom
 * @Date: 2022/5/9 15:55
 */
@Data
@AutoMapper(target = FormTemplate.class)
public class FormTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 表单模板名称
     */
    private String name;

    /**
     * 表单分类 关联 表单分类 id
     */
    private Long category;

    /**
     * 表单分类名称
     */
    @Translation(type = TransConstant.FORM_CATEGORY_ID_TO_NAME, mapper = "category")
    private String categoryName;

    /**
     * 表单类型:0 系统表单 1 自定义表单
     */
    private Integer formType;

    /**
     * 表单设计类型（0-数据优先，1-界面优先，2-简易模板）
     */
    private Integer formDesignType;

    /**
     * 排序号
     */
    private Integer sortCode;

    /**
     * 状态（Y正常 N关闭）
     */
    private String status;

    /**
     * 表单设计json
     */
    private String formJson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private Long createNickName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 功能模块名称
     */
    private String functionalModule;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * Form页面名称
     */
    private String functionFormName;
}
