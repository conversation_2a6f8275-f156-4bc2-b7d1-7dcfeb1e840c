package com.duoom.forms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.DbUtil;
import cn.hutool.db.meta.MetaUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.duoom.common.core.domain.dto.FormTemplateDto;
import com.duoom.common.core.domain.dto.MenuDto;
import com.duoom.common.core.enums.StatusEnum;
import com.duoom.common.core.exception.ServiceException;
import com.duoom.common.core.service.FormTemplateService;
import com.duoom.common.core.service.MenuService;
import com.duoom.common.core.utils.ObjectUtils;
import com.duoom.common.mybatis.core.page.PageQuery;
import com.duoom.common.mybatis.core.page.TableDataInfo;
import com.duoom.forms.constant.FormConstant;
import com.duoom.forms.domain.FormHistory;
import com.duoom.forms.domain.FormTemplate;
import com.duoom.forms.domain.bo.FormDataFirstBo;
import com.duoom.forms.domain.bo.FormTemplateListBo;
import com.duoom.forms.domain.config.ComponentConfig;
import com.duoom.forms.domain.config.FormDesignConfig;
import com.duoom.forms.domain.config.TableConfig;
import com.duoom.forms.domain.config.TableStructureConfig;
import com.duoom.forms.domain.vo.FormTemplateVo;
import com.duoom.forms.mapper.FormHistoryMapper;
import com.duoom.forms.mapper.FormTemplateMapper;
import com.duoom.forms.service.IDataAuthTableRelationService;
import com.duoom.forms.service.IDataLinkService;
import com.duoom.forms.service.IFormTemplateService;
import com.duoom.forms.utils.DatasourceUtil;
import com.duoom.forms.utils.FormSqlUtil;
import com.duoom.forms.utils.FromTemplateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单模版
 *
 * <AUTHOR>
 * @version 25.5.0
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FormTemplateServiceImpl implements FormTemplateService, IFormTemplateService {

    private final FormTemplateMapper formTemplateMapper;

    private final ObjectMapper objectMapper;

    private final IDataLinkService databaselinkService;

    private final IDataAuthTableRelationService dataAuthTableRelationService;

    private final FormHistoryMapper formHistoryMapper;

    private final MenuService menuService;

    /**
     * 根据表单ID查询表单模版 TODO 待实现
     *
     * @param formIds
     * @return
     */
    @Override
    public List<FormTemplateDto> selectListByFormIds(List<Long> formIds) {
        return List.of();
    }


    /**
     * 表单设计-数据优先-新增
     */
    @Override
    public Integer addDataFirst(FormDataFirstBo bo) {

        DataFirst dataFirst = buildDataFirst(bo);

        // 保存数据权限关系
        if (BooleanUtils.isTrue(bo.getFormJson().getIsDataAuth())) {
            saveDataAuth(bo.getFormJson(), dataFirst.tableStructureConfigs);
        }

        int size = formTemplateMapper.insert(dataFirst.template);

        //保存历史版本数据
        createFormHistory(dataFirst.template);

        return size;
    }

    /**
     * 保存表和数据权限关联关系
     */
    private void saveDataAuth(FormDesignConfig formDesignConfig, List<TableStructureConfig> tableStructureConfigs) {
        Optional<TableStructureConfig> maiTableOpt = tableStructureConfigs.stream().filter(TableStructureConfig::getIsMain).findFirst();
        List<Long> dataAuthList = formDesignConfig.getDataAuthList().stream().map(Long::valueOf).collect(Collectors.toList());
        dataAuthTableRelationService.saveDataAuthTableRelations(maiTableOpt.get().getTableName(), dataAuthList);
    }


    /**
     * 创建数据权限
     *
     * @param tableName        表名
     * @param formDesignConfig 表单 json
     */
    @SneakyThrows
    private void createDataAuthField(String tableName, FormDesignConfig formDesignConfig) {
        if (BooleanUtils.isTrue(formDesignConfig.getIsDataAuth())) {
            // 添加权限字段 rule_user_id
            String databaseId = formDesignConfig.getDatabaseId();
            DataSource dataSource = DatasourceUtil.getDataSource(databaseId);
            String[] columnNames = MetaUtil.getColumnNames(dataSource, tableName);
            if (!ArrayUtils.contains(columnNames, FormConstant.AUTH_USER_ID)) {
                DbType dbType = databaselinkService.getDbType(databaseId);
                Db.use(dataSource).executeBatch(FormSqlUtil.buildAddDataAuthFieldSqls(dbType, tableName));
            }
        }
    }

    /**
     * 表单设计模板-列表 - 条件构建
     */
    private LambdaQueryWrapper<FormTemplate> buildWrapper(FormTemplateListBo bo) {
        return new LambdaQueryWrapper<FormTemplate>()
            .like(StrUtil.isNotBlank(bo.getKeyword()), FormTemplate::getName, bo.getKeyword())
            .eq(FormTemplate::getFormType, bo.getType())
            .eq(ObjUtil.isNotNull(bo.getCategory()), FormTemplate::getCategory, bo.getCategory())
            .eq(StrUtil.isNotBlank(bo.getStatus()), FormTemplate::getStatus, bo.getStatus())
            .select(
                FormTemplate::getId, FormTemplate::getName, FormTemplate::getCategory, FormTemplate::getFormType,
                FormTemplate::getFormDesignType, FormTemplate::getCreateTime, FormTemplate::getCreateBy, FormTemplate::getStatus
            );
    }


    /**
     * 表单设计模板-列表
     */
    @Override
    public TableDataInfo<FormTemplateVo> page(FormTemplateListBo bo, PageQuery pageQuery) {

        IPage<FormTemplateVo> page = formTemplateMapper.selectVoPage(pageQuery.build(), buildWrapper(bo));

        // 系统表单 ids
        List<Long> formIds = page
            .getRecords()
            .stream()
            .filter(x -> StatusEnum.NO.getIntValue().equals(x.getFormType()))
            .map(FormTemplateVo::getId)
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(formIds)) {

            // key：表单 id； value：组件路径
            Map<Long, String> menuMap = menuService.selectMenuDtoByFormIds(formIds);

            for (FormTemplateVo record : page.getRecords()) {
                String component = menuMap.get(record.getId());
                if (StrUtil.isNotBlank(component)) {
                    String[] split = component.split(StringPool.SLASH);
                    if (split.length >= 3) {
                        record.setFunctionalModule(split[1]);
                        record.setFunctionName(split[2]);
                    }
                }
            }
        }


        return TableDataInfo.build(page);
    }

    /**
     * 表单设计模板-详情
     */
    @Override
    public FormTemplateVo query(Long id) {

        FormTemplateVo formTemplateVo = formTemplateMapper.selectVoById(id);
        if (ObjUtil.isNull(formTemplateVo)) throw new ServiceException("找不到此数据");

        //系统表单
        if (StatusEnum.NO.getIntValue().equals(formTemplateVo.getFormType())) {
            //去menu表里面找道对应的功能名称和功能模块名称
            MenuDto menu = menuService.selectMenuDtoByFormId(formTemplateVo.getId());
            if (ObjUtil.isNotNull(menu)) {
                String[] split = menu.getComponent().split(StringPool.SLASH);
                formTemplateVo.setFunctionalModule(split[1]);
                formTemplateVo.setFunctionName(split[2]);
            } else {
                throw new ServiceException("找不到对应的系统表单功能名称和功能模块名称");
            }
        }
        return formTemplateVo;
    }

    /**
     * 表单设计模板-批量查询
     */
    @Override
    public List<FormTemplateVo> multiInfo(Long[] ids) {
        return formTemplateMapper.selectVoByIds(Arrays.asList(ids));
    }


    /**
     * 表单设计模板-修改表单状态
     *
     * @param id     表单 id
     * @param status 状态（Y正常 N关闭）
     */
    @Override
    public Integer edit(Long id, String status) {
        return formTemplateMapper.update(
            Wrappers.<FormTemplate>update()
                .lambda()
                .set(FormTemplate::getStatus, status)
                .eq(FormTemplate::getId, id)
        );
    }

    /**
     * 表单设计模板-界面优先或简易模板-新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addCodeFirst(FormDataFirstBo bo) {

        FormTemplate template = BeanUtil.toBean(bo, FormTemplate.class);
        List<TableStructureConfig> tableStructureConfigs = bo.getFormJson().getTableStructureConfigs();

        String databaseId = bo.getFormJson().getDatabaseId();
        bo.getFormJson().setTableConfigs(FromTemplateUtil.wrapperTableConfig(databaseId, tableStructureConfigs));

        template.setFormJson(convertFormJsonToString(bo.getFormJson()));

        // 创建表
        createTableSafely(tableStructureConfigs, databaseId);

        // 保存数据权限关系
        if (BooleanUtils.isTrue(bo.getFormJson().getIsDataAuth())) {
            saveDataAuth(bo.getFormJson(), tableStructureConfigs);
        }

        Integer size = formTemplateMapper.insert(template);
        createFormHistory(template);

        return size;
    }

    /**
     * 将 FormJson 对象转换为字符串
     */
    private String convertFormJsonToString(FormDesignConfig formJson) {
        //因为实体类里面 formJson 是字符串类型 所以必须手动赋值一下   此时的formjson  是转换过tableconfig的
        try {
            return objectMapper.writeValueAsString(formJson);
        } catch (JsonProcessingException e) {
            log.error("表单设计模板，表单json 参数转换异常，{}", e.getMessage());
            throw new ServiceException("操作失败，请稍后再试");
        }
    }

    /**
     * 创建数据库表，并处理可能的 SQLException 异常
     */
    private void createTableSafely(List<TableStructureConfig> tableStructureConfigs, String databaseId) {
        try {
            createTable(tableStructureConfigs, databaseId);
        } catch (SQLException e) {
            log.error("表单设计模板，建表异常，{}", e.getMessage());
            throw new ServiceException("操作失败，请稍后再试");
        }
    }

    /**
     * 表单新增-保存历史版本数据
     */
    private void createFormHistory(FormTemplate template) {
        //保存历史版本数据
        FormHistory formHistory = new FormHistory();
        formHistory.setFormId(template.getId());
        formHistory.setFormJson(template.getFormJson());
        formHistory.setVersion(StatusEnum.YES.getIntValue());//第一个版本
        formHistory.setActivityFlag(StatusEnum.YES.getIntValue());//是活动版本
        formHistoryMapper.insert(formHistory);
    }

    /**
     * 创建表
     * 修改此方法逻辑，一定要对比一下 GeneratorServiceImpl 中的 同名方法  是否 也需要修改！
     *
     * @param tableStructureConfigs 表结构配置
     * @param databaseId            数据库id
     */
    private void createTable(List<TableStructureConfig> tableStructureConfigs, String databaseId) throws SQLException {
        //------------------------------------根据配置建表开始------------------------------------------
        {
            //判断是否为默认数据源
            if (StrUtil.equalsIgnoreCase(databaseId, FormConstant.DEFAULT_DATASOURCE_KEY)) {
                List<String> createTableSqls = DatasourceUtil.wrapperCreateTableSql(tableStructureConfigs, DatasourceUtil.getDataSourceMasterDbType(), databaseId);

                for (String sql : createTableSqls) {
                    DbUtil.use(DatasourceUtil.getDatasourceMaster()).execute(sql);
                }
            } else {
                DbType dbType = databaselinkService.getDbType(databaseId);
                List<String> createTableSqls = DatasourceUtil.wrapperCreateTableSql(tableStructureConfigs, dbType, databaseId);
                for (String sql : createTableSqls) {
                    DbUtil.use(DatasourceUtil.getDataSource(databaseId)).execute(sql);
                }
            }

        }
        //------------------------------------根据配置建表结束------------------------------------------
    }

    /**
     * 表单设计模板-数据优先-修改
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateDataFirst(FormDataFirstBo bo) {

        DataFirst dataFirst = buildDataFirst(bo);

        //查询当前表单模板的version版本
        LambdaQueryWrapper<FormHistory> queryWrapper = Wrappers.lambdaQuery(FormHistory.class)
            .eq(FormHistory::getFormId, dataFirst.template().getId())
            .select(FormHistory::getVersion, FormHistory::getId)
            .orderByDesc(FormHistory::getVersion);
        List<FormHistory> formHistoryList = formHistoryMapper.selectList(queryWrapper);

        //全部设置为非活动版本
        FormHistory formHistory1 = new FormHistory();
        formHistory1.setActivityFlag(StatusEnum.NO.getIntValue());
        formHistoryMapper.update(formHistory1, Wrappers.lambdaQuery(FormHistory.class)
            .eq(FormHistory::getFormId, dataFirst.template().getId())
            .select(FormHistory::getVersion, FormHistory::getId));

        //新增版本
        FormHistory formHistory = new FormHistory();
        formHistory.setFormId(dataFirst.template().getId());
        formHistory.setFormJson(dataFirst.template().getFormJson());
        if (!formHistoryList.isEmpty() && ObjectUtil.isNotEmpty(formHistoryList.get(0).getVersion())) {//兼容以前没有版本的情况,设置为1
            formHistory.setVersion(formHistoryList.get(0).getVersion() + 1);
        } else {
            formHistory.setVersion(StatusEnum.YES.getIntValue());
        }
        formHistory.setActivityFlag(StatusEnum.YES.getIntValue());
        formHistoryMapper.insert(formHistory);

        // 保存数据权限关系
        saveDataAuth(bo.getFormJson(), dataFirst.tableStructureConfigs());

        return formTemplateMapper.updateById(dataFirst.template());
    }

    /**
     * 表单-数据优先（新增与修改公共部分抽取）
     * 1、构建表结构配置
     * 2、创建数据库权限字段
     *
     * @return template:表单设计模板、List<TableStructureConfig>：表结构配置
     */
    private DataFirst buildDataFirst(FormDataFirstBo bo) {

        FormTemplate template = BeanUtil.toBean(bo, FormTemplate.class);

        List<ComponentConfig> list = bo.getFormJson().getFormJson().getList();
        Optional<TableConfig> mainTableConfigOptional = bo.getFormJson().getTableConfigs().stream().filter(TableConfig::getIsMain).findFirst();

        if (mainTableConfigOptional.isEmpty()) {
            throw new ServiceException("主表不存在");
        }

        List<TableStructureConfig> tableStructureConfigs = FromTemplateUtil.wrapperTableStructureConfig(list, mainTableConfigOptional.get());
        bo.getFormJson().setTableStructureConfigs(tableStructureConfigs);
        template.setFormJson(convertFormJsonToString(bo.getFormJson()));

        // 创建数据权限字段
        createDataAuthField(mainTableConfigOptional.get().getTableName(), bo.getFormJson());
        return new DataFirst(template, tableStructureConfigs);
    }

    private record DataFirst(FormTemplate template, List<TableStructureConfig> tableStructureConfigs) {
    }

    /**
     * 表单设计模板-界面优先或简易模板-修改
     */
    @Override
    public Integer updateCodeFirst(FormDataFirstBo bo) {

        FormTemplate template = BeanUtil.toBean(bo, FormTemplate.class);
        List<TableStructureConfig> tableStructureConfigs = bo.getFormJson().getTableStructureConfigs();

        bo.getFormJson().setTableConfigs(new ArrayList<>());

        String databaseId = bo.getFormJson().getDatabaseId();
        DbType dbType = DatasourceUtil.getDbType(databaseId);
        boolean isUpperCase = ArrayUtils.contains(FormConstant.UPPERCASE_DB_TYPE_ARRAY, dbType);

        for (TableStructureConfig tableStructureConfig : tableStructureConfigs) {
            //因为要统一自定义表单存入到数据库的json格式  所以 在这里需要将codefirst的表结构配置json格式转换成datafirst的json格式
            // TableStructureConfig -> TableConfig
            TableConfig tableConfig = new TableConfig();
            tableConfig.setTableName(tableStructureConfig.getTableName());
            tableConfig.setIsMain(tableStructureConfig.getIsMain());
            tableConfig.setPkField(isUpperCase ? StringUtils.upperCase(FormConstant.DEFAULT_PK) : FormConstant.DEFAULT_PK);
            tableConfig.setPkType(FormConstant.DEFAULT_PK_TYPE);
            tableConfig.setRelationField(tableStructureConfig.getIsMain() ? null : isUpperCase ? StringUtils.upperCase(FormConstant.DEFAULT_FK) : FormConstant.DEFAULT_FK);
            tableConfig.setRelationTableField(tableStructureConfig.getIsMain() ? null : isUpperCase ? StringUtils.upperCase(FormConstant.DEFAULT_PK) : FormConstant.DEFAULT_PK);
            bo.getFormJson().getTableConfigs().add(tableConfig);
        }

        createTableSafely(tableStructureConfigs, databaseId);

        template.setFormJson(convertFormJsonToString(bo.getFormJson()));

        //查询当前表单模板的version版本
        LambdaQueryWrapper<FormHistory> queryWrapper = Wrappers.lambdaQuery(FormHistory.class)
            .eq(FormHistory::getFormId, template.getId())
            .select(FormHistory::getVersion, FormHistory::getId)
            .orderByDesc(FormHistory::getVersion);
        List<FormHistory> formHistoryList = formHistoryMapper.selectList(queryWrapper);

        //全部设置为非活动版本
        FormHistory formHistory1 = new FormHistory();
        formHistory1.setActivityFlag(StatusEnum.NO.getIntValue());
        formHistoryMapper.update(formHistory1, Wrappers.lambdaQuery(FormHistory.class)
            .eq(FormHistory::getFormId, template.getId())
            .select(FormHistory::getVersion, FormHistory::getId));

        //新增版本
        FormHistory formHistory = new FormHistory();
        formHistory.setFormId(template.getId());
        formHistory.setFormJson(template.getFormJson());
        if (!formHistoryList.isEmpty() && ObjectUtil.isNotEmpty(formHistoryList.get(0).getVersion())) {//兼容以前没有版本的情况,设置为1
            formHistory.setVersion(formHistoryList.get(0).getVersion() + 1);
        } else {
            formHistory.setVersion(StatusEnum.YES.getIntValue());
        }
        formHistory.setActivityFlag(StatusEnum.YES.getIntValue());
        formHistoryMapper.insert(formHistory);

        // 保存数据权限关系
        saveDataAuth(bo.getFormJson(), tableStructureConfigs);
        return formTemplateMapper.updateById(template);
    }

    /**
     * 表单设计模板-删除
     */
    @Override
    public Integer remove(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        formHistoryMapper.delete(Wrappers.lambdaQuery(FormHistory.class).in(FormHistory::getFormId, idList));
        return formHistoryMapper.deleteByIds(idList);
    }

    /**
     * 根据表单 id 查表单名称
     *
     * @param id 表单分类 Id
     * @return 表单分类名称
     */
    @Override
    public String selectFormTemplateNameById(Long id) {
        FormTemplate formTemplate = formTemplateMapper.selectById(id);
        return ObjectUtils.notNullGetter(formTemplate, FormTemplate::getName);
    }
}
