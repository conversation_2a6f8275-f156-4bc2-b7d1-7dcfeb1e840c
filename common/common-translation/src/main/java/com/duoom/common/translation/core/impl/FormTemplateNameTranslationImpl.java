package com.duoom.common.translation.core.impl;

import com.duoom.common.core.service.FormTemplateService;
import com.duoom.common.translation.annotation.TranslationType;
import com.duoom.common.translation.constant.TransConstant;
import com.duoom.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * 表单分类名翻译实现
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.FORM_TEMPLATE_ID_TO_NAME)
public class FormTemplateNameTranslationImpl implements TranslationInterface<String> {

    private final FormTemplateService formTemplateService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof Long id) {
            return formTemplateService.selectFormTemplateNameById(id);
        }
        return null;
    }
}
