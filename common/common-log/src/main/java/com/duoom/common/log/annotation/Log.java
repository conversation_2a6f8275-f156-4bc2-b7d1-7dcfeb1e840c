package com.duoom.common.log.annotation;

import com.duoom.common.log.enums.BusinessType;
import com.duoom.common.log.enums.OperLogType;
import com.duoom.common.log.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 模块
     */
    String title() default "";

    /**
     * 功能
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;

    /**
     * 排除指定的请求参数
     */
    String[] excludeParamNames() default {};

    /**
     * 日志类型【0：操作日志；1：异常日志：2：请求日志】
     * 操作日志定义：对某个模块进行特殊记录，传值为：OPERATION
     * 请求日志定义：所有 CURD 请求，默认传值
     * 异常日志定义：接口出现异常时，为异常日志。
     * <p>
     * 说明：
     * 只有需对模块记录为操作日志时，才需传值，反之无需传值。
     * 每条日志只有一个类型，例如出现异常，就为异常日志，不记录多条。
     */
    OperLogType operLogType() default OperLogType.REQUEST;

}
