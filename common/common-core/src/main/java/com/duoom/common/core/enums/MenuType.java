package com.duoom.common.core.enums;

/**
 * 菜单枚举
 */
public enum MenuType {
    /**
     * 目录
     */
    DIR("M", "目录"),

    /**
     * 菜单
     */
    MENU("C", "菜单"),

    /**
     * 按钮
     */
    BUTTON("F", "按钮");

    private final String code;
    private final String description;

    MenuType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static MenuType fromCode(String code) {
        for (MenuType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知菜单类型代码: " + code);
    }
}
