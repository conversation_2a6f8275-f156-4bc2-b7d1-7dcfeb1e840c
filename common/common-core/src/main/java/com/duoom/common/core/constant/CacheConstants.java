package com.duoom.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 * @version 25.5.0
 */
public interface CacheConstants {

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 小程序 access_token 缓存 key
     */
    String XCX_ACCESS_TOKEN = "xcx_access_token:";

    /**
     * 国际化语言字段类型
     */
    String I18N_MESSAGE_LANGUAGE = "i18n_message_language";

    /**
     * 多语言配置
     */
    String I18N_MESSAGES = GlobalConstants.GLOBAL_REDIS_KEY + "i18nMessages:";

}
