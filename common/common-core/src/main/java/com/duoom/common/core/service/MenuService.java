package com.duoom.common.core.service;

import com.duoom.common.core.domain.dto.MenuDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 菜单 业务层
 *
 * <AUTHOR>
 */
public interface MenuService {

    /**
     * 新增菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    boolean insertByDto(MenuDto menu);

    /**
     * 修改菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    boolean updateByDtoId(MenuDto menu);

    /**
     * 删除菜单信息
     *
     * @param menuIds 菜单ID集合
     * @return 结果
     */
    boolean deleteMenuByIds(List<Long> menuIds);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    MenuDto selectMenuDtoById(Long menuId);

    /**
     * 根据表单ID查询信息
     *
     * @param FormId 表单ID
     * @return 菜单信息
     */
    MenuDto selectMenuDtoByFormId(Long FormId);

    /**
     * 根据表单 Ids 查询信息
     *
     * @param FormIds 表单 Ids
     * @return 菜单信息 key：表单 id； value：组件路径
     */
    Map<Long, String> selectMenuDtoByFormIds(Collection<Long> FormIds);

    /**
     * 根据菜单 id 查菜单名称
     *
     * @param id 菜单分类 Id
     * @return 菜单名称
     */
    String selectMenuNameById(Long id);

    /**
     * 校验菜单名称是否唯一
     *
     * @param dto 菜单信息
     * @return true：不存在； false：已存在
     */
    boolean checkMenuNameUnique(MenuDto dto);


    /**
     * 删除菜单和角色与菜单的关联
     *
     * @param menuId 菜单 Id
     */
     void deleteRoleMenuById(Long menuId);
}
