package com.duoom.common.core.domain.dto;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 树形结构 sys_tree
 *
 * <AUTHOR>
 */

@Data
public class TreeDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 树接口配置json
     */
    private String config;

    /**
     * 列表配置
     */
    private String columns;

    /**
     * 类型 静态树 1  Api接口树 2
     */
    private Integer type;

    /**
     * 父级图标
     */
    private String parentIcon;

    /**
     * 子级图标
     */
    private String childIcon;

    /**
     * 状态（Y正常 N禁用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
