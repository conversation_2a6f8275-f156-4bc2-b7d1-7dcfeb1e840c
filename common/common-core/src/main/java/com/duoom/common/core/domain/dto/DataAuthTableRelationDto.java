package com.duoom.common.core.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 数据权限 与 表 关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
public class DataAuthTableRelationDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据权限id
     */
    private Long dataAuthId;


}
