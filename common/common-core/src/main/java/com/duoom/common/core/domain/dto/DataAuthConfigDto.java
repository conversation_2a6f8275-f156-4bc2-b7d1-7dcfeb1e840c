package com.duoom.common.core.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 数据权限自定义配置详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Data
public class DataAuthConfigDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 数据权限表id
     */
    private Long dataAuthId;

    /**
     * 排序号
     */
    private Integer orderNumber;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 条件
     */
    private Integer conditionType;

    /**
     * 字段类型
     */
    private Integer fieldType;

    /**
     * 根据字段类型区分 如果是 登陆人信息 默认不需要存储值
     */
    private String fieldValue;


}
