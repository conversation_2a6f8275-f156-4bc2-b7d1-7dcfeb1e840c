package com.duoom.common.web.core;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.duoom.common.core.enums.PlatformType;
import com.duoom.common.core.service.I18nService;
import com.duoom.common.core.utils.StringUtils;
import org.springframework.context.support.AbstractMessageSource;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 基于数据库/接口的国际化消息源（带缓存）
 */
@Component("i18nMessageSource")
public class I18nMessageSource extends AbstractMessageSource {

    private final I18nService i18nService;

    public I18nMessageSource(I18nService i18nService) {
        this.i18nService = i18nService;
    }

    @Override
    protected MessageFormat resolveCode(String code, Locale locale) {
        // 默认服务端类型
        String msg = getMessageFrom(code, locale, PlatformType.SERVER.getCode());
        return new MessageFormat(msg != null ? msg : code, locale);
    }

    /**
     * 支持指定客户端类型
     */
    public MessageFormat resolveCodeWithType(String code, Locale locale, String type) {
        String msg = getMessageFrom(code, locale, type);
        return new MessageFormat(msg != null ? msg : code, locale);
    }

    private String getMessageFrom(String code, Locale locale, String type) {
        Map<String, Object> messages = i18nService.getMessagesByLocale(type,locale.toString());
        return getValueByDotKey(messages,code);
    }

    /**
     * 根据 code（可能包含点 .）从嵌套 Map 中获取对应的值。
     * - 如果 code 不包含点，直接返回 map.get(code)
     * - 如果包含点，则递归获取嵌套 map 中的值
     *
     * @param nestedMap 嵌套或扁平 key 生成的 Map
     * @param code      key，如 "user.not.null" 或 "simpleKey"
     * @return 对应的 value，找不到时返回 null
     */
    public static String getValueByDotKey(Map<String, Object> nestedMap, String code) {
        if (StrUtil.isBlank(code) || MapUtil.isEmpty(nestedMap)) {
            return null;
        }
        // 无嵌套，直接获取并转换为 String
        if (!code.contains(StringUtils.POINT)) {
            Object value = nestedMap.get(code);
            return value != null ? value.toString() : null;
        }

        List<String> keys = StrUtil.split(code, StringUtils.POINT);
        Object current = nestedMap;

        for (String key : keys) {
            if (!(current instanceof Map)) {
                return null;
            }
            current = ((Map<?, ?>) current).get(key);
            if (current == null) {
                return null;
            }
        }

        return current.toString();
    }
}
